"use client";
import React, { useMemo, useRef, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { DatesSetArg, DateSelectArg, EventClickArg, DayHeaderContentArg, MoreLinkArg, EventSegment } from "@fullcalendar/core";
import { IGetInterviewsResponse } from "@/interfaces/interviewInterfaces";
import Button from "../formElements/Button";
import Dark<PERSON><PERSON> from "../svgComponents/DarkCross";
import { EventImpl } from "@fullcalendar/core/internal";
import { useTranslations } from "next-intl";
import "react-loading-skeleton/dist/skeleton.css";
// import Skeleton from "react-loading-skeleton";

interface CalendarProps {
  handleDatesSet: (info: DatesSetArg) => void;
  handleOnSelect: (info: DateSelectArg) => void;
  interviews: Array<IGetInterviewsResponse>;
  handleEventClick: ({ event }: { event: EventImpl }) => void;
  calendarLoading?: boolean;
}

const CommonCalendar: React.FC<CalendarProps> = ({ handleDatesSet, handleOnSelect, interviews, handleEventClick, calendarLoading = false }) => {
  const calendarRef = useRef<FullCalendar>(null);

  const t = useTranslations();
  const [showMoreEventsModal, setShowMoreEventsModal] = useState(false);
  const [events, setEvents] = useState<EventSegment[]>([]);
  const [currentViewType, setCurrentViewType] = useState<string>("dayGridMonth");

  useMemo(() => {
    if (calendarLoading) setShowMoreEventsModal(false);
  }, [calendarLoading]);

  console.log("CommonCalendar render - calendarLoading:", calendarLoading);

  const renderEventContent = (eventInfo: EventClickArg) => {
    return (
      <div className="fc-event-content">
        <div className="fc-event-time">
          {new Date(eventInfo.event.start!).toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: false,
          })}
        </div>
        <div className="fc-event-title">{eventInfo.event.title}</div>
      </div>
    );
  };

  const renderDayHeader = (args: DayHeaderContentArg) => {
    // Only apply custom header in week view
    const date = args.date;
    const dayNumber = date.getDate();
    const weekday = new Intl.DateTimeFormat("en-US", { weekday: "short" }).format(date);
    if (args.view.type === "timeGridWeek") {
      return (
        <div className="custom-day-header">
          <div className="day-number">{dayNumber}</div>
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    } else {
      return (
        <div className="custom-day-header">
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    }
  };

  // const loading = calendarLoading
  //   ? {
  //       dayCellContent: () => (
  //         <div className="skeleton-day-cell">
  //           <Skeleton width={24} height={18} />
  //           <Skeleton width="90%" height={16} borderRadius={4} className="mt-1" />
  //         </div>
  //       ),
  //     }
  //   : {};

  return (
    <div className="calendar-container">
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        headerToolbar={{
          left: "prev,next",
          center: "title",
          right: "dayGridMonth,timeGridWeek",
        }}
        initialView={currentViewType}
        editable={false}
        selectable={true}
        fixedWeekCount={false}
        dayMaxEvents={true}
        weekends={true}
        events={interviews}
        select={handleOnSelect}
        eventClick={handleEventClick}
        height="66vh"
        datesSet={(info) => {
          handleDatesSet(info);
          setCurrentViewType(info.view.type);
        }}
        eventContent={renderEventContent}
        // dayHeaderFormat={{ weekday: "short", day: "numeric" }}
        dayHeaderContent={renderDayHeader}
        slotLabelFormat={{
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        }}
        eventTimeFormat={{
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        }}
        moreLinkClick={(arg: MoreLinkArg) => {
          setEvents(arg.allSegs);
          setShowMoreEventsModal(true);
          return "none";
        }}
        // {...loading}
      />
      <div className={`${showMoreEventsModal ? "open" : ""} calendar-more-events-modal`}>
        <div className="events-modal-header">
          <div>
            <p>{t("all_events")}</p>
            {events.length && events[0].event.start ? (
              <p className="date-day">
                {new Date(events[0].event.start).toLocaleDateString("en-US", { weekday: "short", day: "numeric", month: "short" })}
              </p>
            ) : null}
          </div>
          <Button className="clear-btn p-0" onClick={() => setShowMoreEventsModal(false)}>
            <DarkCross />
          </Button>
        </div>
        <div className="events-modal-body">
          {events.map((event) => (
            <div className="events-list-card" key={event.event.id} onClick={() => handleEventClick({ event: event.event as EventImpl })}>
              {event.event.start ? (
                <span className="time-text">
                  {new Date(event.event.start).toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: false })}
                </span>
              ) : null}
              <p className="event-text">{event.event.title}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default React.memo(CommonCalendar);
