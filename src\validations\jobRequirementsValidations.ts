/**
 * Job Requirements Validation Schema using Yup
 */

import * as yup from "yup";

// Type for the form data based on Yup schema
export type GenerateJobSchema = yup.InferType<ReturnType<typeof generateJobValidation>>;

/**
 * Generate job requirements validation schema with translations
 * @param translation - Function to translate error message keys
 * @returns Yup validation schema for job requirements
 */
export const generateJobValidation = (translation: (key: string) => string) =>
  yup.object().shape({
    title: yup
      .string()
      .trim()
      .required(translation("title_required"))
      .min(3, translation("title_min"))
      .max(150, translation("title_max"))
      .matches(/^[A-Za-z\s\/&,.'()+#\-:]+$/, translation("title_alpha_only")),
    employment_type: yup.string().trim().required(translation("employment_type_required")),
    department_id: yup.string().trim().required(translation("department_id_required")),
    salary_range: yup
      .string()
      .trim()
      .required(translation("salary_range_required"))
      .test("is-valid-salary-range", translation("salary_range_format"), (value) => {
        if (!value) return false;
        const match = value.match(/^\$([0-9,]+) \- \$([0-9,]+)$/);
        if (!match) return false;

        const min = parseInt(match[1].replace(/,/g, ""), 10);
        const max = parseInt(match[2].replace(/,/g, ""), 10);

        return min > 0 && max > 0 && max > min;
      }),
    salary_cycle: yup.string().trim().required(translation("salary_cycle_required")),
    location_type: yup.string().trim().required(translation("location_type_required")),
    state: yup
      .string()
      .trim()
      .required(translation("state_required"))
      .min(3, translation("state_min"))
      .max(50, translation("state_max"))
      .matches(/^[A-Za-z\s]+$/, translation("state_alpha_only")),
    city: yup
      .string()
      .trim()
      .required(translation("city_required"))
      .min(3, translation("city_min"))
      .max(50, translation("city_max"))
      .matches(/^[A-Za-z\s]+$/, translation("city_alpha_only")),
    role_overview: yup.string().trim().required(translation("role_overview_required")).min(3, translation("role_overview_min")),
    experience_level: yup.string().trim().required(translation("experience_level_required")),
    responsibilities: yup.string().trim().required(translation("responsibilities_required")).min(3, translation("responsibilities_min")),
    educations_requirement: yup.string().trim().required(translation("education_required")).min(3, translation("education_min")),
    certifications: yup.string().trim().optional(),
    skills_and_software_expertise: yup.string().trim().required(translation("skills_required")).min(3, translation("skills_min")),
    experience_required: yup
      .string()
      .trim()
      .required(translation("experience_required_required"))
      .matches(/^[0-9]+(\.[0-9]+)?$/, translation("experience_must_be_number"))
      .test("min-value", translation("experience_min_value"), (value) => {
        const numValue = parseFloat(value);
        return numValue > 0;
      })
      .test("max-value", translation("experience_max_value"), (value) => {
        const numValue = parseFloat(value);
        return numValue <= 50;
      })
      .test("max-length", translation("experience_max_length"), (value) => value.length <= 4),
    ideal_candidate_traits: yup.string().trim().required(translation("traits_required")).min(3, translation("traits_min")),
    about_company: yup.string().trim().required(translation("company_required")).min(3, translation("company_min")),
    perks_benefits: yup.string().trim().optional(),
    tone_style: yup.string().trim().required(translation("tone_required")),
    additional_info: yup.string().trim().optional(),
    compliance_statement: yup
      .array()
      .of(yup.string())
      .required(translation("compliance_required"))
      .min(1, translation("compliance_min"))
      .typeError(translation("compliance_type_error")),
    show_compliance: yup.boolean().oneOf([true], translation("show_compliance_required")).required(translation("show_compliance_required")),
  });

export default generateJobValidation;
