"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Image from "next/image";

import logo from "../../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import styles from "@/styles/auth.module.scss";
import { resetPasswordValidation } from "@/validations/authValidations";
import { resetPassword } from "@/services/authServices";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError } from "@/utils/helper";
import hidePassword from "../../../../public/assets/images/hide-password.svg";
import showPassword from "../../../../public/assets/images/show-password.svg";
import { useTranslate } from "@/utils/translationUtils";

const ResetPassword = () => {
  const t = useTranslations();
  const translate = useTranslate();

  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState(true);
  const [confirmPassword, setConfirmPassword] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const emailParam = searchParams?.get("email");
  const otpParam = searchParams?.get("otp");

  const email = emailParam ? decodeURIComponent(emailParam) : "";
  const otp = otpParam ? decodeURIComponent(otpParam) : "";

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(resetPasswordValidation(translate)),
  });

  const onSubmit = async (data: { new_password: string; confirm_password: string }) => {
    setLoading(true);
    try {
      const { new_password } = data;
      const resetData = {
        password: new_password,
        email,
        otp,
      };
      const result = await resetPassword(resetData);
      if (result?.data?.success) {
        setLoading(false);
        toastMessageSuccess(translate(result?.data?.message as string));
        router.push(routes.LOGIN);
      } else {
        setLoading(false);
        toastMessageError(translate((result?.data?.message as string) ?? "something_went_wrong"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={ClientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        {t("reset_heading")} <span>{t("password_heading")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="new_password" required>
                          {t("new_password")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="new_password"
                          type={password ? "password" : "text"}
                          align="right"
                          placeholder={t("password_placeholder")}
                          iconClass="icon-align"
                        >
                          <InputWrapper.Icon onClick={() => setPassword(!password)}>
                            <Image src={!password ? hidePassword : showPassword} alt="password-icon" />
                          </InputWrapper.Icon>
                        </Textbox>
                        <InputWrapper.Error message={errors?.new_password?.message || ""} />
                      </InputWrapper>

                      <InputWrapper>
                        <InputWrapper.Label htmlFor="confirm_password" required>
                          {t("confirm_password")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="confirm_password"
                          type={confirmPassword ? "password" : "text"}
                          placeholder={t("confirm_password_placeholder")}
                          iconClass="icon-align"
                          align="right"
                        >
                          <InputWrapper.Icon onClick={() => setConfirmPassword(!confirmPassword)}>
                            <Image src={!confirmPassword ? hidePassword : showPassword} alt="password-icon" />
                          </InputWrapper.Icon>
                        </Textbox>
                        <InputWrapper.Error message={errors?.confirm_password?.message || ""} />
                      </InputWrapper>
                      <Button loading={loading} disabled={loading} className="primary-btn rounded-md w-100 mt-5">
                        {t("reset_password")}
                      </Button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
