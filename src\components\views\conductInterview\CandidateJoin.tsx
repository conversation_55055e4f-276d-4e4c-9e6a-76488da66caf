import React, { useState, useEffect, useRef } from "react";

import <PERSON><PERSON> from "lottie-react";
import Image from "next/image";

import { useForm, SubmitHandler } from "react-hook-form";
import { useSearchParams } from "next/navigation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslations } from "next-intl";

import {
  LocalUser,
  RemoteUser,
  useJoin,
  useLocalCameraTrack,
  useLocalMicrophoneTrack,
  usePublish,
  useRemoteUsers,
  useConnectionState,
  useRTCClient,
} from "agora-rtc-react";

import Button from "@/components/formElements/Button";
import NextArrowIcon from "@/components/svgComponents/NextArrowIcon";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import Loader from "@/components/loader/Loader";

import { getAgoraToken } from "@/services/agoraService";
import { AGORA_AUDIO_CONSTRAINTS, AGORA_CONNECTION_STATE, AGORA_USER_TYPE, SOCKET_ROUTES } from "@/constants/commonConstants";
import { AgoraTokenData } from "@/interfaces/agoraInterfaces";

import { decryptInfo, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { EmailValidation } from "@/validations/authValidations";

import logo from "../../../../public/assets/images/logo.svg";
import LeaveIcon from "@/components/svgComponents/LeaveIcon";
import candidateInterview from "../../../../public/assets/images/candidate-Interview.json";
import style from "../../../styles/conductInterview.module.scss";
import VideoCallIcon from "@/components/svgComponents/VideoCallIcon";
import MicIcon from "@/components/svgComponents/MicIcon";
import { io, Socket } from "socket.io-client";
import toast from "react-hot-toast";
import { useTranslate } from "@/utils/translationUtils";

const CandidateJoin = () => {
  const t = useTranslations();
  const translate = useTranslate();
  const searchParams = useSearchParams();
  const encryptedParams = searchParams?.get("params");

  const [params, setParams] = useState<{
    interviewId: string;
    channelName: string;
    candidateName: string;
    interviewerName: string;
    candidateId: string;
  }>({
    interviewId: "",
    channelName: "",
    candidateName: "",
    interviewerName: "",
    candidateId: "",
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [tokenData, setTokenData] = useState<AgoraTokenData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error] = useState("");
  const [joining, setJoining] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [isMicOn, setIsMicOn] = useState(true); // Start with mic ON to avoid issues
  const socketRef = useRef<Socket | null>(null);
  const noRemoteUsersTimerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<{ email: string }>({ resolver: yupResolver(EmailValidation(translate)) });

  // Only join after form submission and token retrieval
  const joinOptions = tokenData
    ? {
        appid: process.env.NEXT_PUBLIC_AGORA_APP_ID!,
        channel: tokenData.channelName,
        token: tokenData.token,
        uid: tokenData.uid,
      }
    : { appid: "", channel: "", token: "", uid: "" };

  const { localMicrophoneTrack } = useLocalMicrophoneTrack(isMicOn, AGORA_AUDIO_CONSTRAINTS);
  const { localCameraTrack } = useLocalCameraTrack(isCameraOn);
  useJoin(joinOptions, joining && !!tokenData);
  usePublish([localMicrophoneTrack, localCameraTrack]);
  const remoteUsers = useRemoteUsers();
  const connectionState = useConnectionState();
  const client = useRTCClient();
  const initialFetchDone = useRef(false);

  console.log("client########=======>>>>>candidate", client);
  console.log("remoteUsers#########=====>>>>>candidate", remoteUsers);
  console.log("joinOptions######", joinOptions);

  // Disable picture-in-picture for all video elements
  useEffect(() => {
    const disablePictureInPicture = (video: HTMLVideoElement) => {
      // Set the disablePictureInPicture property
      video.disablePictureInPicture = true;

      // Set controlsList attribute to disable picture-in-picture
      video.setAttribute("controlsList", "nofullscreen nodownload noremoteplayback nopictureinpicture");
      video.setAttribute("disablePictureInPicture", "true");

      // Prevent picture-in-picture events
      const preventPiP = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
      };

      video.addEventListener("enterpictureinpicture", preventPiP, true);
      video.addEventListener("leavepictureinpicture", preventPiP, true);
    };

    const processAllVideos = () => {
      const videos = document.querySelectorAll("video");
      videos.forEach((video) => disablePictureInPicture(video as HTMLVideoElement));
    };

    // Create a MutationObserver to watch for new video elements
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.tagName === "VIDEO") {
              disablePictureInPicture(element as HTMLVideoElement);
            } else {
              const videos = element.querySelectorAll("video");
              videos.forEach((video) => disablePictureInPicture(video as HTMLVideoElement));
            }
          }
        });
      });
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Process existing videos
    processAllVideos();

    // Also run periodically as a fallback
    const interval = setInterval(processAllVideos, 1000);

    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    if (encryptedParams) {
      try {
        console.log("encryptedParams", encryptedParams);
        const params = decryptInfo(encryptedParams);
        console.log("params", params);
        const parsedParams = JSON.parse(params);
        console.log("parsedParams", parsedParams);
        setParams(parsedParams);
      } catch (error) {
        console.error("Error decrypting params:", error);
        toastMessageError(t("invalid_invitation_link"));
      }
    }
  }, [encryptedParams]);

  // Convert connection state to title case (first letter capital, rest lowercase)
  const formatConnectionState = (state: string): string => {
    if (!state) return "";
    return state.charAt(0).toUpperCase() + state.slice(1).toLowerCase();
  };

  useEffect(() => {
    toast.dismiss();
    if (
      tokenData?.token &&
      (formatConnectionState(connectionState) === AGORA_CONNECTION_STATE.DISCONNECTED ||
        formatConnectionState(connectionState) === AGORA_CONNECTION_STATE.DISCONNECTING)
    ) {
      toastMessageError(formatConnectionState(connectionState));
      console.log("connectionState", connectionState);
    } else if (tokenData?.token) {
      toastMessageSuccess(formatConnectionState(connectionState));
      console.log("connectionState1233", connectionState);
    }
  }, [connectionState]);

  console.log("channelName,,,,,,,,,,,,,", params.channelName);

  const connectSocket = (candidateId: string, interviewId: string) => {
    if (socketRef.current) {
      socketRef.current.disconnect(); // Disconnect the previous socket connection
    }

    // Initialize the socket with the new token
    socketRef.current = io(process.env.NEXT_PUBLIC_SOCKET_CONNECTION_URL!, {
      path: SOCKET_ROUTES.CANDIDATE_CONDUCT_INTERVIEW,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      query: {
        interviewId,
      },
    });

    // Add socket event listeners
    socketRef.current?.on("connect", () => {
      console.log("Connected to server");
      if (socketRef.current) {
        socketRef.current.emit("candidate_joined", {
          candidateId,
        });
      }
    });

    socketRef.current?.on("disconnect", (reason, details) => {
      console.log("Disconnected from server:", reason, details);
    });

    socketRef.current?.on("connect_error", (error) => {
      console.log("Connect error:", error);
    });
  };

  useEffect(() => {
    if (initialFetchDone.current) return;
    if (tokenData?.token && params.candidateId && params.interviewId) {
      connectSocket(params.candidateId, params.interviewId);
    }
    initialFetchDone.current = true;
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [tokenData?.token, params.candidateId, params.interviewId]);

  // send message to server every 10 seconds
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (socketRef.current?.connected && tokenData?.token && params.candidateId) {
      interval = setInterval(() => {
        if (socketRef.current) {
          socketRef.current.emit("message", {
            candidateId: params.candidateId,
          });
        }
      }, 10000);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tokenData?.token, params.candidateId, socketRef.current?.connected]);

  // end call if no remote users for 2 minutes
  useEffect(() => {
    // Only start monitoring after joining the call
    if (!joining || !formSubmitted) return;

    // Clear any existing timer when component updates
    if (noRemoteUsersTimerRef.current) {
      clearTimeout(noRemoteUsersTimerRef.current);
      noRemoteUsersTimerRef.current = null;
    }

    // If there are remote users, we don't need a timer
    if (remoteUsers.length > 0) return;

    // Start a 2-minute timer when there are no remote users
    noRemoteUsersTimerRef.current = setTimeout(
      () => {
        toastMessageError(t("interviewer_disconnected"));
        endCall();
      },
      2 * 60 * 1000
    ); // 2 minutes in milliseconds

    // Cleanup function to clear the timer when component unmounts or dependencies change
    return () => {
      if (noRemoteUsersTimerRef.current) {
        clearTimeout(noRemoteUsersTimerRef.current);
        noRemoteUsersTimerRef.current = null;
      }
    };
  }, [remoteUsers.length, joining, formSubmitted]);

  // Handle form submission
  const onSubmit: SubmitHandler<{ email: string }> = async (data) => {
    console.log("data=====>", data);
    console.log("params=====>", params);
    if (!params.interviewId || !params.channelName) {
      toastMessageError(t("something_went_wrong"));
      return;
    }

    try {
      setLoading(true);
      // Call API to get token for candidate
      const response = await getAgoraToken({
        interviewId: params.interviewId,
        personType: AGORA_USER_TYPE.Candidate,
        channelName: params.channelName,
        candidateEmail: data.email,
        candidateId: params.candidateId,
      });
      if (response?.data?.success && response?.data?.data) {
        setTokenData(response.data.data);
        setFormSubmitted(true);
        setJoining(true);
        setIsCameraOn(true);
        setIsMicOn(true);
      } else {
        toastMessageError(t(response?.data?.message));
        setFormSubmitted(false);
      }
    } catch (error) {
      console.log("error on submit====>>>>>", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  // End the call
  const endCall = async () => {
    setJoining(false);
    if (client) {
      await client.leave();
    }
    if (localCameraTrack) {
      localCameraTrack.close();
    }
    if (localMicrophoneTrack) {
      localMicrophoneTrack.close();
    }
    setIsCameraOn(false);
    setIsMicOn(false);
    // Redirect to a thank you page or show a message
    setFormSubmitted(false);
  };

  return (
    <div className={style.conduct_interview}>
      <div className="container-fluid">
        {!error && !formSubmitted ? (
          <div className={style.candidate_confirmation}>
            <div className="row align-items-center">
              <div className="col-lg-7 col-md-12 col-sm-12">
                <Lottie animationData={candidateInterview} loop={false} className={style.candidate_interview_lottie} />
              </div>
              <div className="col-lg-5 col-md-8 col-sm-12 m-auto">
                <div className={style.confirmation}>
                  <Image src={logo} alt="logo" className={style.confirmation_logo} />
                  <h3 className={style.confirmation_title}>{t("join_your_online_interview")}</h3>
                  <p className={style.confirmation_text}>{t("you_re_about_to_start_your_online_interview")}</p>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <div className={style.confirmation_input_group}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="email" required>
                          {t("email")}
                        </InputWrapper.Label>
                        <Textbox
                          className="form-control"
                          control={control}
                          name="email"
                          type="text"
                          placeholder={t("enter_your_email")}
                          required
                        ></Textbox>
                        <Button
                          className={`primary-btn rounded-md button-sm ${style.confirmation_input_group_icon}`}
                          disabled={loading ? true : false}
                          type="submit"
                        >
                          {loading ? <Loader className="ms-2" /> : <NextArrowIcon />}
                        </Button>
                        <InputWrapper.Error message={errors?.email?.message || ""} />
                      </InputWrapper>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        ) : (
          !error && (
            <div className="container">
              <div className={style.video_call_container}>
                <div className={style.video_participants_grid}>
                  <div className="row">
                    {/* Local User Video */}
                    <div className="col-md-6">
                      <div className={style.video_participant_box}>
                        <div className={style.participant_name}>{params.candidateName}</div>
                        <LocalUser
                          audioTrack={localMicrophoneTrack}
                          videoTrack={localCameraTrack}
                          cameraOn={isCameraOn}
                          micOn={isMicOn}
                          className={style.video_feed}
                          playAudio={false}
                          playVideo={true}
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      {/* Remote User Video (Interviewer) */}
                      <div className={style.video_participant_box}>
                        {remoteUsers.length > 0 ? (
                          <>
                            <div className={style.participant_name}>{params.interviewerName}</div>
                            <RemoteUser user={remoteUsers[0]} playVideo={true} playAudio={true} className={style.video_feed}>
                              <div className={style.remote_user_info}>
                                {params.interviewerName} ({t("interviewer")})
                              </div>
                            </RemoteUser>
                          </>
                        ) : (
                          <div className={style.empty_video}>
                            <p>{t("waiting_for_interviewer")}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="videos-btn-container candidate-join-btn-container">
                    <Button className="videos-btn danger" onClick={endCall}>
                      <LeaveIcon className="videos-btn-icon" />
                      {t("leave")}
                    </Button>
                    <Button className="videos-btn secondary" onClick={() => setIsCameraOn(!isCameraOn)}>
                      <VideoCallIcon className="videos-btn-icon" isVideoMute={!isCameraOn} />
                      {t("cam")} {isCameraOn}
                    </Button>
                    <Button className="videos-btn secondary" onClick={() => setIsMicOn(!isMicOn)}>
                      <MicIcon className="videos-btn-icon" isMicMute={!isMicOn} />
                      {t("mic")} {isMicOn}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default CandidateJoin;
