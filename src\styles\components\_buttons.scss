@use "../abstracts" as *;

//common button style ----------
.theme-btn {
  padding: 12px 30px;
  font-size: $text-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.4s all;
  font-weight: 600;
  white-space: nowrap;

  &:active {
    opacity: 0.7;
  }

  @media screen and (max-width: 767px) {
    padding: 1rem 1.5rem;
  }

  &.rounded-sm {
    border-radius: 8px;
  }

  &.rounded-md {
    border-radius: 12px;
  }

  &.rounded-lg {
    border-radius: 20px;
  }

  &.button-lg {
    padding: 16px;
  }

  &.button-sm {
    padding: 8px 15px;
    font-size: $text-sm;
  }

  &.button-full {
    padding: 1rem 1.5rem;
    font-size: $text-md;
    width: 100%;
    justify-content: center;

    svg {
      fill: $white;
    }
  }

  &.primary-outline-btn {
    border: 1px solid $primary;
    background-color: $white;
    color: $primary;

    &:hover {
      background: $primary;
      color: $white;
      border-color: $white;
    }
  }

  &.dark-outline-btn {
    border: 1px solid $dark;
    background-color: transparent;
    color: $dark;

    &:hover {
      background: transparent;
      color: $primary;
      border-color: $primary;
    }
  }

  &.danger-outline-btn {
    border: 1px solid $danger;
    background-color: transparent;
    color: $danger;

    svg {
      fill: $danger;
      stroke: $danger;
    }

    &:hover {
      background: $danger;
      color: $white;
      border-color: $white;
      svg {
        fill: $white;
        stroke: $white;
      }
    }
    // &:hover {
    //   background: transparent;
    //   color: $dark;
    //   border-color: $dark;
    //   svg {
    //     fill: $dark;
    //     stroke: $dark;
    //   }
    // }
  }

  &.danger-outline-btn-hover-none {
    border: 1px solid $danger;
    background-color: transparent;
    color: $danger;
    svg {
      fill: $danger;
      stroke: $danger;
    }
  }
  &.danger-btn {
    background: $danger;
    border: 1px solid $danger;
    color: $white;

    &:hover {
      background: $dark;
      border-color: $dark;
    }
  }

  &.primary-btn {
    background: $primary;
    border: 1px solid $primary;
    color: $white;

    &:hover {
      background: $dark;
    }

    &.truly-disabled {
      background: $primary;
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        background: $primary;
        border: 1px solid $primary;
        color: $white;
      }
    }
  }
  &.green-btn {
    background: $green;
    border: 1px solid $green;
    color: $white;
  }

  &.secondary-btn {
    background: $secondary;
    border: 2px solid $secondary;
    color: $white;

    &:hover {
      background: $secondary;
      color: $white;
    }
  }

  &.disable-btn {
    background: rgba($dark, 0.2);
    color: rgba($dark, 0.5);
    border: 1px solid transparent;
    cursor: default;
  }

  &.white-btn {
    // border: 1px solid $borderColor;
    color: $primary;
    background-color: $white;
    padding-inline: 10px;

    &:hover {
      background: $primary;
      color: $white;
      border-color: $white;

      svg {
        path {
          stroke: $white;
        }
      }
    }
  }

  &.dark-btn {
    background: $dark;
    border: 1px solid $dark;
    color: $white;

    &:hover {
      background: $secondary;
      border-color: $secondary;
    }
  }

  &.success-btn {
    background: #28a745;
    border: 1px solid #28a745;
    color: $white;

    &:hover {
      background: #28a745;
      border-color: #28a745;
    }
  }

  &.clear-btn {
    background-color: transparent;
    color: $white;
    border: none;

    &.primary {
      color: $primary;
      svg {
        fill: $primary;
      }
    }
    &.secondary {
      color: $secondary;
      svg {
        fill: $secondary;
      }
    }
  }

  &.padding-lg {
    padding: 10px 40px;
  }

  &.minWidth {
    min-width: 110px;
  }

  &.secondary-outline-btn {
    border: 1px solid $secondary;
    background-color: transparent;
    color: $secondary;

    &:hover {
      background-color: $secondary;
      color: $white;
      border-color: $secondary;
    }
  }

  &.text-btn {
    &.secondary {
      color: $secondary;

      &:hover {
        color: $dark;

        svg {
          fill: $dark;
        }
      }
    }

    &.primary {
      color: $primary;

      svg {
        fill: $primary;
      }

      &:hover {
        color: $dark;

        svg {
          fill: $dark;
        }
      }
    }
  }

  .spinner-border {
    width: 14px;
    height: 14px;
    margin-left: 5px;
    border-width: 2px;
  }

  &.btn-xs {
    padding: 10px;
    font-size: $text-xs;
    border-radius: 8px;
  }
}

.text-loader {
  --bs-spinner-width: 14px !important;
  --bs-spinner-height: 14px !important;
}

.videos-btn-container {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  padding: 0;
  justify-content: center;
  flex-wrap: wrap;

  &.candidate-join-btn-container {
    padding: 0;
  }

  .videos-btn {
    display: flex;
    flex-direction: column;
    gap: 5px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 1.4rem;
    font-weight: $semiBold;
    padding: 0;

    .videos-btn-icon {
      padding: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;

      svg {
        width: 22px;
        min-width: 22px;
        height: 22px;
        min-height: 22px;
      }
    }

    &.secondary {
      color: rgba($dark, 0.7);

      .videos-btn-icon {
        background: $secondary;
      }
    }

    &.primary {
      color: $primary;

      .videos-btn-icon {
        background: $primary;
      }
    }

    &.danger {
      color: #f04438;

      .videos-btn-icon {
        background: #f04438;
      }
    }
  }

  .rounded360 {
    transform: rotate(360deg);
    transition: transform 1s ease-in-out;
  }
  @media (max-width: 991px) {
    padding-top: 15px;
    .videos-btn {
      font-size: 1.2rem;
      .videos-btn-icon {
        padding: 8px;
      }
    }
  }
}
.logout-btns {
  display: flex;
  gap: 15px;
  align-items: center;
  @media (max-width: 767px) {
    flex-direction: column;
    .login-btn {
      font-size: 1.4rem !important;
    }
  }

  .login-btn {
    border: 1px solid;
    border-color: $white;
    padding: 10px 36px;
    border-radius: 16px;
    color: $dark;
    background-color: $white;
    font-weight: $medium;
    font-size: 1.6rem;
    transition: all 0.4s ease;
    &:hover {
      background: $primary;
      border-color: $primary;
      color: $white;
      transition: all 0.4s ease;
    }
    &.outline-btn {
      border-color: rgba($white, 0.5);
      background-color: transparent;
      color: $white;
      font-weight: $medium;
    }
    @media (max-width: 1199px) and (min-width: 768px) {
      padding: 10px 20px;
      font-size: 1.4rem;
    }
  }
  &.secondary-logout-btns {
    .login-btn {
      background: $primary;
      color: $white;
      &.outline-btn {
        border-color: $primary;
        background-color: transparent;
        color: $primary;
        font-weight: $medium;
      }
    }
  }
}
