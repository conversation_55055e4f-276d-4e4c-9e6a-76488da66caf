import Button from "@/components/formElements/Button";
import SecondaryChatIcon from "@/components/svgComponents/SecondaryChatIcon";
import ROUTES from "@/constants/routes";
import { IPendingInterview } from "@/services/interviewServices";
import { getOrdinalSuffix } from "@/utils/helper";
import { useTranslate } from "@/utils/translationUtils";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React from "react";
import Skeleton from "react-loading-skeleton";

const PendingInterviewFeedback = ({
  pendingInterviewsLoading,
  pendingInterviews,
}: {
  pendingInterviewsLoading: boolean;
  pendingInterviews: IPendingInterview[];
}) => {
  const router = useRouter();
  const t = useTranslate();
  return (
    <div className="pending-feedbacks-section">
      <h3>{t("pending_feedbacks")}</h3>
      <div className="row">
        {pendingInterviewsLoading ? (
          // Show skeleton loaders during loading
          <>
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <div className="col-md-3" key={`skeleton-${i}`}>
                  <div className="pending-feedbacks__content">
                    <Skeleton height={24} width="80%" style={{ marginBottom: "10px" }} />
                    <Skeleton height={18} width="60%" style={{ marginBottom: "10px" }} />
                    <Skeleton height={18} width="90%" style={{ marginBottom: "15px" }} />
                    <Skeleton height={30} width="70%" />
                  </div>
                </div>
              ))}
          </>
        ) : pendingInterviews.length > 0 ? (
          // Show actual interview data
          pendingInterviews.map((interview, index) => (
            <div className="col-md-3" key={interview.interviewId}>
              <div className={`pending-feedbacks__content ${index === pendingInterviews.length - 1 ? "border-none" : ""}`}>
                <h4 className="pending-feedbacks__content__position">{interview.candidateName}</h4>
                <p className="pending-feedbacks__content__date">
                  {interview.jobTitle} | {interview.roundNumber}
                  {getOrdinalSuffix(interview.roundNumber)} {t("round")}
                </p>
                <p className="pending-feedbacks__content__date">
                  {dayjs(interview.date).format("MMM DD, YYYY")} | {dayjs(interview.startTime).format("HH:mm")} -{" "}
                  {dayjs(interview.endTime).format("HH:mm")}
                </p>
                <Button
                  className="clear-btn p-0 secondary mt-3"
                  onClick={() => router.push(`${ROUTES.INTERVIEW.INTERVIEW_FEEDBACK}/${interview.interviewId}`)}
                >
                  <SecondaryChatIcon className="me-2 p-1" /> {t("pending_analysis")}
                </Button>
              </div>
            </div>
          ))
        ) : (
          // Show message when no pending interviews
          <div className="col-12 text-center">
            <p>{t("no_pending_interviews_found")}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PendingInterviewFeedback;
