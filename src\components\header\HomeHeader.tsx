"use client";
import React, { useState } from "react";
import Link from "next/link";
import Button from "../formElements/Button";
import Image from "next/image";
import Logo from "../../../public/assets/images/logo.svg";
import styles from "@/styles/header.module.scss";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import ROUTES from "@/constants/routes";
import { STRATUM9_MAIN_WEB_URL } from "@/constants/commonConstants";

const HomeHeader = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated, isLoading } = useAuth();

  const pathname = usePathname();
  const navigate = useRouter();

  console.log(">>>>>>>>>>>>>>>>>>>>>>pathname", pathname);
  console.log("Authentication status:", { isAuthenticated, isLoading });

  return (
    <header className={`${styles.home_header}   `}>
      {/* add class "scrolling" when scroll the page */}
      <nav className="navbar">
        <div className="container">
          {/* Logo */}
          <Link href={ROUTES.HOME} className="navbar-brand">
            <Image src={Logo} alt="logo" width={640} height={320} className={styles.logo} />
          </Link>

          {/* Desktop Buttons */}
          {pathname === ROUTES.HOME ? (
            <div className={styles.desktopButtons}>
              <div className="logout-btns">
                {!isLoading ? (
                  !isAuthenticated ? (
                    <>
                      <Button className="login-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                        Sign Up
                      </Button>
                      <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                        Log In
                      </Button>
                    </>
                  ) : (
                    <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                      Back to Dashboard
                    </Button>
                  )
                ) : null}
                <Button className="login-btn outline-btn" onClick={() => window.open(STRATUM9_MAIN_WEB_URL, "_blank")}>
                  Back to Stratum 9
                </Button>
              </div>
            </div>
          ) : null}

          {/* Hamburger Button (mobile only) */}
          <button className={`${styles.hamburgerSM} ${isOpen ? styles.active : ""}`} onClick={() => setIsOpen(!isOpen)}>
            <span />
            <span />
            <span />
          </button>
        </div>

        {/* Overlay */}
        <div className={`${styles.overlay} ${isOpen ? styles.show : ""}`} onClick={() => setIsOpen(false)} />

        {/* Right Side Menu (mobile only) */}
        {pathname === ROUTES.HOME ? (
          <div className={`${styles.sideMenu} ${isOpen ? styles.open : ""}`}>
            <div className="logout-btns secondary-logout-btns">
              {!isLoading ? (
                !isAuthenticated ? (
                  <>
                    <Button className="login-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                      Sign Up
                    </Button>
                    <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                      Log In
                    </Button>
                  </>
                ) : (
                  <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                    Back to Dashboard
                  </Button>
                )
              ) : null}
              <Button className="login-btn outline-btn" onClick={() => window.open(STRATUM9_MAIN_WEB_URL, "_blank")}>
                Back to Stratum 9
              </Button>
            </div>
          </div>
        ) : null}
      </nav>
    </header>
  );
};

export default HomeHeader;
