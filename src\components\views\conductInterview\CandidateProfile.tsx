import React, { use, useEffect, useState } from "react";
import { Bar, Radar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  RadialLinearScale,
  CategoryScale,
  PointElement,
  LinearScale,
  BarElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { buildStyles, CircularProgressbar } from "react-circular-progressbar";
import Image from "next/image";
import Avatar from "react-avatar";
import { useRouter } from "next/navigation";
// import { useSelector } from "react-redux";
import { useTranslations } from "use-intl";

import AiMarkIcon from "@/components/svgComponents/AiMarkIcon";
import AIVerifiedIcon from "@/components/svgComponents/AIVerifiedIcon";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import CheckSecondaryIcon from "@/components/svgComponents/CheckSecondaryIcon";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import StarIcon from "@/components/svgComponents/StarIcon";
import Button from "@/components/formElements/Button";
import Loader from "@/components/loader/Loader";
import ResumeModal from "@/components/commonModals/ResumeModal";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";

import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import type {
  BarChartData,
  CandidateProfileProps,
  CandidateProfileResponse,
  ICandidateInterviewHistory,
  IFinalAssessment,
  ISkillSpecificAssessment,
  RadarChartData,
} from "@/interfaces/candidatesInterface";
// import { AuthState } from "@/redux/slices/authSlice";
import {
  fetchCandidateProfile,
  getApplicationFinalSummary,
  getCandidateInterviewHistory,
  updateJobApplicationStatus,
  generateFinalSummary,
} from "@/services/CandidatesServices/candidatesApplicationServices";
import { toastMessageError, toastMessageSuccess, toTitleCase } from "@/utils/helper";
import { PERMISSION } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";

import noImageFound from "../../../../public/assets/images/noImageFound.svg";

import "react-circular-progressbar/dist/styles.css";
import FinalAssessmentIcon from "@/components/svgComponents/FinalAssessmentIcon";
import { createFinalAssessment } from "@/services/assessmentService";
import ROUTES from "@/constants/routes";
// import QuestionGeneratorLoader from "@/components/loader/QuestionGeneratorLoader";
// import FinalAssessmentConfirmModal from "@/components/commonModals/FinalAssessmentConfirmModal";
import Skeleton from "react-loading-skeleton";
import style from "../../../styles/conductInterview.module.scss";
import "react-loading-skeleton/dist/skeleton.css";
// import RejectedIcon from "@/components/svgComponents/RejectedIcon";
import ApprovedIcon from "@/components/svgComponents/ApprovedIcon";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";
import RejectedIcon from "@/components/svgComponents/RejectedIcon";
import FinalAssessmentGeneratorLoader from "@/components/loader/QuestionGeneratorLoader";
import { useTranslate } from "@/utils/translationUtils";

export interface IAssessmentStatus {
  exists: boolean;
  isAssessmentShared: boolean;
  isAssessmentSubmitted: boolean;
  assessmentId: number | null;
}

const CandidateProfile: React.FC<CandidateProfileProps> = ({ params }) => {
  // ============================================================================
  // HOOKS AND EXTERNAL DATA
  // ============================================================================

  const router = useRouter();
  // const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations();
  const translate = useTranslate();

  // Extract candidate ID from params
  const paramsPromise = use(params);
  const jobApplicationId = paramsPromise.jobApplicationId;

  // ============================================================================
  // STATE DECLARATIONS
  // ============================================================================

  // UI State - Controls tab selection and active skill display
  /** Controls which main tab is active (true = Skill Assessment, false = Interview History) */
  const [selectedTab, setSelectedTab] = useState<boolean>(false);
  /** Currently selected skill tab for detailed view */
  const [activeSkillTab, setActiveSkillTab] = useState<string>("");
  /** Animation value for circular progress bar (0-100) */
  const [animationValue, setAnimationValue] = useState<number>(0);

  // Data State - Stores fetched candidate information
  /** Main candidate profile data from API */
  const [candidateProfileData, setCandidateProfileData] = useState<CandidateProfileResponse | null>(null);
  /** Candidate's interview history across all rounds */
  const [candidateInterviewHistory, setCandidateInterviewHistory] = useState<ICandidateInterviewHistory[]>([]);
  /** Detailed skill-specific assessment data */
  const [skillSpecificAssessment, setSkillSpecificAssessment] = useState<ISkillSpecificAssessment | null>(null);
  /** Final assessment summary and recommendations */
  const [finalSummary, setFinalSummary] = useState<IFinalAssessment | null>(null);

  // Loading State - Tracks API call progress
  /** Loading state for final summary and skill assessment API call (combined) */
  const [isLoadingFinalSummary, setIsLoadingFinalSummary] = useState<boolean>(false);
  /** Loading state for interview history API call */
  const [isLoadingInterviewHistory, setIsLoadingInterviewHistory] = useState<boolean>(false);
  /** Processing state for hire/reject actions */
  const [isHireRejectCandidateProcessing, setIsHireRejectCandidateProcessing] = useState<boolean>(false);
  /** Loading state for generate final summary action */
  /** Flag to track if user has generated final summary */

  // Data Loaded Flags - Prevents unnecessary API calls
  /** Flag indicating if assessment data (both final and skill) has been loaded */
  // const [assessmentDataLoaded, setAssessmentDataLoaded] = useState<boolean>(false);
  /** Flag indicating if candidate profile data has been loaded */
  const [candidateProfileLoading, setCandidateProfileLoading] = useState<boolean>(false);

  // const searchParams = useSearchParams() || new URLSearchParams();
  const [isGenerationFinalAssessment, setIsGenerationFinalAssessment] = useState(false);
  // const [assessmentStatus, setAssessmentStatus] = useState<IAssessmentStatus>();
  // const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Confirmation modal states for different actions
  const [confirmationModal, setConfirmationModal] = useState({
    isOpen: false,
    action: "",
    title: "",
    message: "",
    confirmButtonText: "",
    onConfirm: () => {},
    loading: false,
  });

  const [resumeModal, setResumeModal] = useState<{ isOpen: boolean; resumeLink: string | null }>({ isOpen: false, resumeLink: null });

  // Permission checks
  const hasHireCandidatePermission = useHasPermission(PERMISSION.HIRE_CANDIDATE);
  const hasManageCandidateProfilePermission = useHasPermission(PERMISSION.MANAGE_CANDIDATE_PROFILE);
  const hasCandidateActionPermission = hasHireCandidatePermission || hasManageCandidateProfilePermission;

  // const showFinalAssessmentConfirmation = () => {
  //   if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
  //     toastMessageError(t("job_id_and_job_application_id_are_required"));
  //     return;
  //   }
  //   setShowConfirmModal(true);
  // };

  // Helper functions to show confirmation modals for different actions
  const showCreateFinalAssessmentConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "createFinalAssessment",
      title: t("create_final_assessment"),
      message: t("final_assessment_warning_message"),
      confirmButtonText: t("create"),
      onConfirm: handleCreateFinalAssessmentConfirmed,
      loading: false,
    });
  };

  const showGenerateFinalSummaryConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "generateFinalSummary",
      title: t("generate_final_summary"),
      message:
        "Once the final summary is generated for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final summary for this candidate.",
      confirmButtonText: t("generate"),
      onConfirm: handleGenerateFinalSummaryConfirmed,
      loading: false,
    });
  };

  const showHireConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "hire",
      title: t("hire_candidate"),
      message: t("are_you_sure_you_want_to_hire_this_candidate"),
      confirmButtonText: t("hire"),
      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.HIRED),
      loading: false,
    });
  };

  const showRejectConfirmation = () => {
    setConfirmationModal({
      isOpen: true,
      action: "reject",
      title: t("reject_candidate"),
      message: t("are_you_sure_you_want_to_reject_this_candidate"),
      confirmButtonText: t("reject"),
      onConfirm: () => handleHireRejectCandidateConfirmed(APPLICATION_STATUS.FINAL_REJECT),
      loading: false,
    });
  };

  const closeConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      action: "",
      title: "",
      message: "",
      confirmButtonText: "",
      onConfirm: () => {},
      loading: false,
    });
  };

  /**
   * Handle creating final assessment (confirmed action)
   */
  const handleCreateFinalAssessmentConfirmed = async () => {
    if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
      toastMessageError(t("job_id_and_job_application_id_are_required"));
      closeConfirmationModal();
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setIsGenerationFinalAssessment(true);

      const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });
      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate(response?.data?.message || "final_assessment_created_successfully"));

        // Get the finalAssessmentId from the response data
        const finalAssessmentId = response.data.data?.assessmentId;

        if (finalAssessmentId) {
          // Redirect to the final assessment page with the finalAssessmentId and default status values
          // For a newly created assessment, both isShared and isSubmitted will be false
          router.push(
            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
          );
        }
      } else {
        toastMessageError(translate(response?.data?.message || "failed_to_create_final_assessment"));
      }
    } catch (error) {
      console.error("Error creating final assessment:", error);
      toastMessageError(translate("an_error_occurred_while_creating_the_final_assessment"));
    } finally {
      setIsGenerationFinalAssessment(false);
      closeConfirmationModal();
    }
  };

  /**
   * Handle creating final assessment (original function for backward compatibility)
   */
  // const handleCreateFinalAssessment = async () => {
  //   if (!candidateProfileData?.jobId || !candidateProfileData?.jobApplicationId) {
  //     toastMessageError(t("job_id_and_job_application_id_are_required"));
  //     return;
  //   }

  //   try {
  //     setIsLoading(true);
  //     setButtonState(BUTTON_STATES.GENERATING);
  //     // setShowConfirmModal(false);
  //     const response = await createFinalAssessment({ jobId: candidateProfileData?.jobId, jobApplicationId: candidateProfileData?.jobApplicationId });
  //     if (response && response.data && response.data.success) {
  //       toastMessageSuccess(t(response?.data?.message || "final_assessment_created_successfully"));

  //       // Get the finalAssessmentId from the response data
  //       const finalAssessmentId = response.data.data?.assessmentId;

  //       if (finalAssessmentId) {
  //         // Redirect to the final assessment page with the finalAssessmentId and default status values
  //         // For a newly created assessment, both isShared and isSubmitted will be false
  //         router.push(
  //           `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${finalAssessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
  //         );
  //       }
  //     } else {
  //       toastMessageError(t(response?.data?.message || "failed_to_create_final_assessment"));
  //     }
  //   } catch (error) {
  //     console.error("Error creating final assessment:", error);
  //     toastMessageError(t("an_error_occurred_while_creating_the_final_assessment"));
  //   } finally {
  //     setIsLoading(false);
  //     setButtonState(BUTTON_STATES.IDLE);
  //   }
  // };

  /**
   * Close the confirmation modal
   */
  // const handleCancelConfirmModal = () => {
  //   setShowConfirmModal(false);
  // };

  // Effect to check assessment status when component mounts
  // useEffect(() => {
  //   // Automatically call getAssessmentStatus when the component mounts
  //   const checkStatus = async () => {
  //     if (!jobApplicationId) {
  //       return;
  //     }

  //     try {
  //       setIsLoading(true);
  //       const response = await getAssessmentStatus(jobApplicationId);

  //       if (response?.data) {
  //         const assessmentData = response?.data?.data;

  //         // Update assessment status state
  //         setAssessmentStatus({
  //           exists: !!assessmentData.assessmentId, //exists: assessmentData.id ? true : false
  //           isAssessmentShared: assessmentData.isAssessmentShared || false,
  //           isAssessmentSubmitted: assessmentData.isAssessmentSubmitted || false,
  //           assessmentId: assessmentData.assessmentId || null,
  //         });
  //       } else {
  //         toastMessageError(t(response?.data?.message || "failed_to_get_assessment_status"));
  //       }
  //     } catch (error) {
  //       console.error(error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   checkStatus();
  // }, [t]);
  // ============================================================================
  // CHART CONFIGURATION
  // ============================================================================

  // Register Chart.js components
  ChartJS.register(RadialLinearScale, CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Generates bar chart data for skill visualization
   * @returns BarChartData object configured for skill scores display
   */
  const generateBarChartData = (): BarChartData => {
    const labels = skillSpecificAssessment?.skillsScores.map((item) => item.skill_name) || [];
    const values = skillSpecificAssessment?.skillsScores.map((item) => item.skill_marks * 10) || [];

    return {
      labels,
      datasets: [
        {
          data: values,
          backgroundColor: labels.map((label, index) => {
            const skillScore = values[index];
            // Color changes only for perfect scores (100/100)
            return skillScore === 100 ? "#ffc107" : "rgba(119,167,255,0.8)";
          }),
          borderRadius: 8,
          borderSkipped: false,
          barPercentage: 0.5,
        },
      ],
    };
  };

  /**
   * Generates radar chart data for behavioral assessment visualization
   * @returns RadarChartData object configured for behavioral metrics
   */
  const generateRadarChartData = (): RadarChartData => {
    console.log("finalSummary===============>", finalSummary);
    // Dynamic portion - behaviouralScores is a Record<string, number> | null
    const labels = finalSummary?.behaviouralScores ? Object.keys(finalSummary.behaviouralScores) : [];
    const values = finalSummary?.behaviouralScores ? Object.values(finalSummary.behaviouralScores) : [];

    return {
      labels: labels,
      datasets: [
        {
          label: "Behavioral Assessment",
          data: values,
          fill: true,
          backgroundColor: "rgba(49, 65, 75, 0.2)",
          borderColor: "rgb(54, 162, 235)",
          pointBackgroundColor: "rgb(54, 162, 235)",
          pointBorderColor: "#fff",
          pointHoverBackgroundColor: "#fff",
          pointHoverBorderColor: "rgb(54, 162, 235)",
        },
      ],
    };
  };

  /**
   * Calculates the number of filled bars for skill success probability visualization
   * @param probability - Success probability percentage (0-100)
   * @returns Number of bars to fill (0-10)
   */
  const calculateFilledBars = (probability: number): number => {
    return Math.round(probability / 10);
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  /** Chart data for bar chart visualization */
  const barChartData = generateBarChartData();

  /** Chart data for radar chart visualization */
  const radarChartData = generateRadarChartData();

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  /**
   * Handles switching to Interview History view
   * Loads interview history data if not already loaded
   *
   * @async
   * @function handleInterviewHistoryClick
   * @returns {Promise<void>}
   */
  const handleInterviewHistoryClick = async (): Promise<void> => {
    console.log(">>>>>>>>>>>>>>>>handleInterviewHistoryClick selectedTab", selectedTab);

    if (!selectedTab) {
      return;
    }

    setSelectedTab(false);

    // Load interview history data if not already loaded

    loadCandidateInterviewHistory(candidateInterviewHistory.length ? false : true);
  };

  /**
   * Handles switching to Skill Specific Assessment view
   * Only works if final summary has been generated
   *
   * @async
   * @function handleSkillAssessmentClick
   * @returns {Promise<void>}
   */
  const handleSkillAssessmentClick = async (): Promise<void> => {
    console.log(">>>>>>>>>>>>>>>>handleSkillAssessmentClick selectedTab", selectedTab);

    if (!candidateProfileData?.isFinalSummaryGenerated || selectedTab) return;
    setSelectedTab(true);
    console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>finalSummary?.skillSummary", finalSummary?.skillSummary);

    // Load assessment data if not already loaded (both final summary and skill data)
    loadFinalSummary(finalSummary && finalSummary?.skillSummary && finalSummary?.skillSummary?.finalSummary?.length ? false : true);
  };

  // useEffect(() => {
  //   if (candidateProfileData) {
  // Update local state to match API data
  // setHasFinalSummaryBeenGenerated(candidateProfileData.isFinalSummaryGenerated);
  // Trigger API call if final summary is generated
  // if (candidateProfileData.isFinalSummaryGenerated) {
  //   loadFinalSummary();
  // } else {
  //   // Clear data if no final summary
  //   setFinalSummary(null);
  //   setSkillSpecificAssessment(null);
  //   setAssessmentDataLoaded(false);
  //   // Ensure Interview History tab is selected
  //   setSelectedTab(false);
  // }
  //   }
  // }, [candidateProfileData]);

  /**
   * Handles hire or reject candidate action (confirmed action)
   * Updates the job application status and refreshes all data
   *
   * @async
   * @function handleHireRejectCandidateConfirmed
   * @param {string} status - The new application status (HIRED or FINAL_REJECT)
   * @returns {Promise<void>}
   */
  const handleHireRejectCandidateConfirmed = async (status: string): Promise<void> => {
    if (!candidateProfileData) {
      closeConfirmationModal();
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      setIsHireRejectCandidateProcessing(true);

      const response = await updateJobApplicationStatus(Number(jobApplicationId), status);

      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate(response.data.message));
        await loadCandidateProfile();
      } else {
        toastMessageError(translate(response?.data?.message));
      }
    } catch (error) {
      console.error("Error updating job application status:", error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setIsHireRejectCandidateProcessing(false);
      closeConfirmationModal();
    }
  };
  /**
   * Loads candidate interview history from the API
   * Prevents multiple simultaneous calls using loading state
   *
   * @async
   * @function loadCandidateInterviewHistory
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingInterviewHistory state
   * - Updates candidateInterviewHistory state on success
   * - Shows error toast on failure
   */
  const loadCandidateInterviewHistory = async (showLoader = true): Promise<void> => {
    if (isLoadingInterviewHistory) return;

    if (showLoader) setIsLoadingInterviewHistory(true);
    try {
      const response = await getCandidateInterviewHistory(jobApplicationId);
      if (response?.data?.success) {
        setCandidateInterviewHistory(response.data.data);
      } else {
        toastMessageError(translate(response?.data?.message));
      }
    } catch (error) {
      console.error("Error loading interview history:", error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setIsLoadingInterviewHistory(false);
    }
  };

  /**
   * Loads final assessment and skill score data from the API
   * Prevents multiple simultaneous calls using loading state
   * Now loads both final assessment and skill score data in a single API call
   *
   * @async
   * @function loadFinalSummary
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates isLoadingFinalAssessment state
   * - Updates finalSummary state on success
   * - Updates skillSpecificAssessment state on success
   * - Updates finalAssessmentLoaded and skillAssessmentLoaded flags
   * - Shows error toast on failure
   */
  const loadFinalSummary = async (showLoader = true): Promise<void> => {
    if (isLoadingFinalSummary) return;

    if (showLoader) setIsLoadingFinalSummary(true);
    try {
      const response = await getApplicationFinalSummary(jobApplicationId);
      if (response?.data?.success) {
        // Extract final summary data
        if (response.data.data.formattedFinalSummary) {
          setFinalSummary(response.data.data.formattedFinalSummary);
        }

        // Extract skill score data
        if (response.data.data.candidateProfileSkillScoreData) {
          setSkillSpecificAssessment(response.data.data.candidateProfileSkillScoreData);
        }
      } else {
        toastMessageError(translate(response?.data?.message));
      }
    } catch (error) {
      console.error("Error loading final assessment:", error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setIsLoadingFinalSummary(false);
    }
  };

  /**
   * Loads candidate profile data from the API
   *
   * @async
   * @function loadCandidateProfile
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Updates candidateProfileData state on success
   * - Shows error toast on failure
   */
  const loadCandidateProfile = async (): Promise<void> => {
    setCandidateProfileLoading(true);

    try {
      const response = await fetchCandidateProfile(jobApplicationId);

      if (response?.data?.success) {
        const candidateInfo = response.data.data;
        setCandidateProfileData(candidateInfo);
        if (candidateInfo.isFinalSummaryGenerated) {
          setSelectedTab(true);
          // Load final assessment data if it has been generated
          loadFinalSummary();
        }
      } else {
        toastMessageError(translate(response?.data?.message));
      }
      setCandidateProfileLoading(false);
    } catch (error) {
      console.error("Error loading candidate profile:", error);
      toastMessageError(translate("something_went_wrong"));
      setCandidateProfileLoading(false);
    }
  };

  /**
   * Refreshes all candidate data by resetting loaded flags and reloading data
   * Used after status updates to ensure data consistency
   *
   * @async
   * @function refreshAllData
   * @returns {Promise<void>}
   *
   * Side effects:
   * - Resets all data loaded flags
   * - Reloads candidate profile
   * - Reloads tab-specific data based on current tab
   */
  // const refreshAllData = async (): Promise<void> => {
  //   // Reset loaded flags to force refresh
  //   // Refresh candidate profile
  //   await loadCandidateProfile();

  //   // Refresh assessment data based on current tab
  //   if (selectedTab) {
  //     await loadFinalSummary();
  //   } else {
  //     await loadCandidateInterviewHistory();
  //   }
  // };

  /**
   * Handles generating final summary for the candidate (confirmed action)
   * Triggers API call to generate comprehensive final summary based on interview data
   *
   * @async
   * @function handleGenerateFinalSummaryConfirmed
   * @returns {Promise<void>}
   */
  const handleGenerateFinalSummaryConfirmed = async (): Promise<void> => {
    if (!candidateProfileData?.jobApplicationId) {
      closeConfirmationModal();
      return;
    }
    if (confirmationModal.loading || candidateProfileData?.isFinalSummaryGenerated) {
      return;
    }

    try {
      // Update modal loading state
      setConfirmationModal((prev) => ({ ...prev, loading: true }));
      // setIsGeneratingFinalSummary(true);

      const response = await generateFinalSummary(jobApplicationId);
      if (response?.data?.success) {
        toastMessageSuccess(translate(response?.data?.message || "final_summary_generated_successfully"));
        // Set flag to indicate final summary has been generated by user
        setCandidateProfileData({ ...candidateProfileData, isFinalSummaryGenerated: true });
        // Switch to Skill Specific Assessment tab automatically
        setSelectedTab(true);
        // Load assessment data (both final summary and skill data)
        await loadFinalSummary();
      } else {
        toastMessageError(translate(response?.data?.message || "failed_to_generate_final_summary"));
      }
    } catch (error) {
      console.error("Error generating final summary:", error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      // setIsGeneratingFinalSummary(false);
      closeConfirmationModal();
    }
  };

  // ============================================================================
  // COMPUTED VALUES AND MEMOIZED DATA
  // ============================================================================

  /**
   * Memoized AI interviewer analysis for the current user
   * Finds analysis data where the interviewer ID matches the current user
   */
  // const aiInterviewerAnalysis = useMemo(
  //   () =>
  //     candidateInterviewHistory?.find((record) => record.interviewerId === authData?.id && record.interviewerPerformanceAiAnalysis)
  //       ?.interviewerPerformanceAiAnalysis,
  //   [candidateInterviewHistory, authData?.id]
  // );

  /**
   * Memoized all interviewer analysis data for admin users
   * Returns all interviewer performance AI analysis data for admin users
   */
  // const allInterviewerAnalysis = useMemo(() => {
  //   if (!candidateInterviewHistory || !authData || authData.account_type !== "admin") return [];

  //   return candidateInterviewHistory
  //     .filter((record) => record.interviewerPerformanceAiAnalysis)
  //     .map((record) => ({
  //       interviewerId: record.interviewerId,
  //       interviewerName: record.interviewerName,
  //       interviewerImage: record.interviewerImage,
  //       roundNumber: record.roundNumber,
  //       analysis: record.interviewerPerformanceAiAnalysis,
  //     }));
  // }, [candidateInterviewHistory, authData]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  /**
   * Initial data loading effect
   * Loads candidate profile and interview history data in parallel when component mounts or candidateId changes
   */
  useEffect(() => {
    const initializeData = async () => {
      // Load candidate profile and interview history data in parallel
      await Promise.all([loadCandidateProfile(), loadCandidateInterviewHistory()]);
    };

    initializeData();
  }, [jobApplicationId]);

  /**
   * Effect to set the first skill as active when skill assessment data is loaded
   * Ensures there's always an active skill tab when data is available
   */
  useEffect(() => {
    if (skillSpecificAssessment?.skillsScores && skillSpecificAssessment.skillsScores.length > 0 && !activeSkillTab) {
      setActiveSkillTab(skillSpecificAssessment.skillsScores[0].skill_name);
    }
  }, [skillSpecificAssessment, activeSkillTab]);

  /**
   * Effect to animate the circular progress bar
   * Creates a smooth animation from 0 to the final success probability value
   */
  useEffect(() => {
    const timer = requestAnimationFrame(() => setAnimationValue(finalSummary?.overallSuccessProbability || 0));
    return () => cancelAnimationFrame(timer);
  }, [finalSummary?.overallSuccessProbability]);

  // ============================================================================
  // RENDER LOGIC
  // ============================================================================

  /**
   * Renders the skill details section for the selected skill
   * Shows strengths, potential gaps, and success probability
   */
  const renderSkillDetails = () => {
    if (!skillSpecificAssessment?.skillsScores || !activeSkillTab) return null;

    const selectedSkill = skillSpecificAssessment.skillsScores.find((skill) => skill.skill_name === activeSkillTab);

    if (!selectedSkill) return null;

    const probability = selectedSkill.probability_of_success_in_this_skill?.probabilityOfSuccessInSkill || 0;
    const filledBars = calculateFilledBars(probability);

    return (
      <div className="row">
        <div className="col-md-7">
          {selectedSkill.strengths?.strengths && <h4 className="skill-sub-title">Strengths</h4>}
          <ul className="strengths">
            {selectedSkill.strengths?.strengths?.map((strength, index) => (
              <li key={index} className="strength-item">
                {strength}
              </li>
            ))}
          </ul>
          {selectedSkill.potentials_gaps?.potentialGaps && <h4 className="skill-sub-title">Potential Gaps</h4>}
          <ul className="strengths">
            {selectedSkill.potentials_gaps?.potentialGaps?.map((gap, index) => (
              <li key={index} className="strength-item">
                {gap}
              </li>
            ))}
          </ul>
        </div>
        <div className="col-md-5">
          <div className="probability-card">
            <h4 className="skill-sub-title"> Success Probability</h4>
            <div className="progress-container">
              <h3 className="ms-2 fw-bold">{probability}%</h3>
              <div className="probability-bar">
                {Array.from({ length: 10 }, (_, index) => (
                  <div key={index} className={`bar ${index < filledBars ? "filled" : ""}`} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <FinalAssessmentGeneratorLoader show={isGenerationFinalAssessment} />
      {/* old final summary confirmation modal not in use now */}
      {/* {showConfirmModal && (
        <FinalAssessmentConfirmModal onClickCancel={handleCancelConfirmModal} onClickGenerate={handleCreateFinalAssessment} disabled={isLoading} />
      )} */}

      {/* Confirmation Modal for various actions */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={closeConfirmationModal}
        onConfirm={confirmationModal.onConfirm}
        title={confirmationModal.title}
        message={confirmationModal.message}
        confirmButtonText={confirmationModal.confirmButtonText}
        loading={confirmationModal.loading}
        loadingText={t("processing")}
      />

      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon
                    onClick={() => {
                      router.back();
                    }}
                  />
                  {t("candidate")} <span>{t("profile")} </span>
                </h2>

                <div className="button-align">
                  {candidateProfileLoading || isGenerationFinalAssessment ? (
                    <Skeleton width={100} height={30} borderRadius={12} />
                  ) : hasCandidateActionPermission &&
                    candidateProfileData?.candidateName &&
                    (candidateProfileData?.isFinalAssessmentGenerated ||
                      (!candidateProfileData?.isFinalAssessmentGenerated && !candidateProfileData?.isFinalSummaryGenerated)) ? (
                    <Button
                      className="theme-btn clear-btn text-btn primary p-0 m-0"
                      onClick={() => {
                        if (!candidateProfileData?.isFinalAssessmentGenerated) {
                          showCreateFinalAssessmentConfirmation();
                        } else if (candidateProfileData?.assessmentId) {
                          // Redirect to the final assessment page with the existing assessment ID
                          router.push(
                            `${ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${candidateProfileData?.assessmentId}&jobId=${candidateProfileData?.jobId}&jobApplicationId=${candidateProfileData?.jobApplicationId}`
                          );
                        }
                      }}
                      disabled={isGenerationFinalAssessment}
                    >
                      <FinalAssessmentIcon className="me-2 p-1" />
                      {candidateProfileData?.isFinalAssessmentGenerated ? t("view_final_assessment") : t("create_final_assessment")}
                    </Button>
                  ) : null}

                  {/* Conditionally render resume preview button if resumeLink exists */}
                  {hasCandidateActionPermission && candidateProfileData && candidateProfileData.resumeLink && (
                    <>
                      <Button
                        onClick={() => setResumeModal({ isOpen: true, resumeLink: candidateProfileData?.resumeLink })}
                        className="theme-btn clear-btn text-btn primary p-0 m-0"
                      >
                        <PreviewResumeIcon className="me-2 p-1" />
                        {t("preview_candidate_resume")}
                      </Button>
                      <ResumeModal
                        isOpen={resumeModal.isOpen}
                        onClose={() => setResumeModal({ ...resumeModal, isOpen: false })}
                        resumeLink={resumeModal.resumeLink}
                      />
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="inner-section profile-section">
            <div className="candidate-profile">
              {candidateProfileLoading ? (
                <Skeleton height={100} width={100} borderRadius={12} />
              ) : (
                <Avatar
                  src={candidateProfileData?.imageUrl || undefined}
                  name={candidateProfileData?.candidateName || ""}
                  size="100"
                  round={true}
                  className="candidate-image"
                />
              )}
              <div className="candidate-info">
                {/*  Use candidateName from API */}
                <h3 className="candidate-name">
                  {candidateProfileLoading ? <Skeleton width={150} height={16} /> : toTitleCase(candidateProfileData?.candidateName || "-")}
                </h3>

                <div className="info-container">
                  <div className="info-item">
                    <p className="info-title">{t("post_applied_for")}</p>
                    {/* Use jobTitle from API */}
                    <p className="info-value">
                      {candidateProfileLoading ? <Skeleton width={100} height={16} /> : candidateProfileData?.jobTitle || "-"}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("department")}</p>
                    <p className="info-value">
                      {candidateProfileLoading ? <Skeleton width={100} height={16} /> : candidateProfileData?.department || "-"}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("current_round")}</p>
                    <p className="info-value">
                      {candidateProfileLoading ? <Skeleton width={20} height={16} /> : candidateProfileData?.roundNumber || 0}
                    </p>
                  </div>
                  <div className="info-item">
                    <p className="info-title">{t("resume_approved_by")}</p>
                    <p className="info-value with-img">
                      {candidateProfileLoading ? (
                        <Skeleton width={16} height={16} borderRadius={100} />
                      ) : candidateProfileData?.interviewerName ? (
                        <Image src={candidateProfileData?.interviewerImage || noImageFound} alt="Interviewer avatar" height={20} width={20} />
                      ) : null}
                      {/*  Use interviewerName from API */}
                      {candidateProfileLoading ? <Skeleton width={100} height={16} /> : candidateProfileData?.interviewerName || "-"}
                    </p>
                  </div>

                  {candidateProfileLoading ? (
                    <Skeleton height={47} width={225} borderRadius={12} />
                  ) : candidateProfileData ? (
                    <div className="button-align">
                      {/* Show Generate Final Summary button if summary hasn't been generated yet */}
                      {candidateProfileData.isFinalSummaryGenerated ? (
                        // Final summary generated → show decision status or hire/reject buttons
                        candidateProfileData.status === APPLICATION_STATUS.HIRED ? (
                          <ApprovedIcon className="final-decision-icon" />
                        ) : candidateProfileData.status === APPLICATION_STATUS.FINAL_REJECT ? (
                          <RejectedIcon className="final-decision-icon" />
                        ) : // If not decided yet, show Hire/Reject buttons

                        ![APPLICATION_STATUS.HIRED, APPLICATION_STATUS.FINAL_REJECT].includes(candidateProfileData?.status) &&
                          hasHireCandidatePermission ? (
                          <>
                            <Button
                              className="primary-btn rounded-md minWidth"
                              onClick={showHireConfirmation}
                              disabled={isHireRejectCandidateProcessing || !candidateProfileData}
                            >
                              {isHireRejectCandidateProcessing ? <Loader /> : t("hire")}
                            </Button>
                            <Button
                              className="dark-outline-btn rounded-md minWidth"
                              onClick={showRejectConfirmation}
                              disabled={isHireRejectCandidateProcessing || !candidateProfileData}
                            >
                              {isHireRejectCandidateProcessing ? <Loader /> : t("reject")}
                            </Button>
                          </>
                        ) : null
                      ) : (
                        // Always show Generate Final Summary button if summary not generated
                        hasCandidateActionPermission && (
                          <Button
                            className="primary-btn rounded-md minWidth"
                            onClick={showGenerateFinalSummaryConfirmation}
                            disabled={!candidateProfileData?.jobApplicationId}
                          >
                            {t("generate_final_summary")}
                          </Button>
                        )
                      )}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            </div>
            {/* Interview History Button - Always visible */}
            {!candidateProfileData?.isFinalSummaryGenerated && (
              <div className="mb-4">
                <h2>{t("interview_history")}</h2>
              </div>
            )}

            {/* Skill Specific Assessment Tab - Only visible after final summary generation */}
            {candidateProfileData?.isFinalSummaryGenerated && (
              <div className="common-tab mb-5">
                <li className={selectedTab ? "active" : ""} onClick={handleSkillAssessmentClick}>
                  {t("skill_specific_assessment")}
                </li>
                <li className={!selectedTab ? "active" : ""} onClick={handleInterviewHistoryClick}>
                  {t("interview_history")}
                </li>
              </div>
            )}
            {selectedTab &&
              (isLoadingFinalSummary ? (
                // Loading state - show comprehensive skeleton loader
                <div className="assessment-content">
                  <div className="row g-4">
                    <div className="col-md-4">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                    <div className="col-md-8">
                      <div className="improvement-areas-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                  </div>
                  <div className="row g-4">
                    <div className="col-12 col-md-6 d-flex">
                      <div className="skills-score-card skills-graph-card flex-grow-1">
                        <Skeleton height={300} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                    <div className="col-12 col-md-6 d-flex">
                      <div className="skills-score-card skills-graph-card flex-grow-1">
                        <Skeleton height={300} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                  </div>
                  <div className="row g-4">
                    <div className="col-lg-4">
                      <div className="summary-text-card skills-score-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                    <div className="col-lg-8">
                      <div className="summary-text-card skills-summary-card">
                        <Skeleton height={200} width={"100%"} borderRadius={20} />
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="improvement-areas-card">
                        <Skeleton height={18} width={"20%"} borderRadius={6} />
                        <div className="row g-3 mt-3">
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                          <div className="col-md-4">
                            <Skeleton height={177} width={"100%"} borderRadius={24} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : finalSummary ? (
                // Data available - show actual content
                <div className="assessment-content">
                  <div className="row g-4">
                    {finalSummary && finalSummary.overallSuccessProbability ? (
                      <div className="col-md-4">
                        <div className="circular-progress-card">
                          <CircularProgressbar
                            className="circular-progress-bar"
                            value={animationValue}
                            text={`${animationValue}%`}
                            circleRatio={0.5}
                            strokeWidth={16}
                            styles={buildStyles({
                              rotation: 0.749,
                              strokeLinecap: "butt",
                              trailColor: "#eee",
                              textColor: "#333",
                              textSize: "1.6rem",
                              pathColor: "#9ebff7",
                              pathTransitionDuration: 0.5,
                            })}
                          />
                        </div>
                      </div>
                    ) : null}
                    <div className="col-md-8">
                      {finalSummary && finalSummary.skillSummary && (
                        <div className="summary-text-card row">
                          {finalSummary.skillSummary.finalSummary.length > 0 && (
                            <div className="col-md-9">
                              <h3 className="sub-tittle mt-0">
                                <AiMarkIcon className="me-2" /> {t("ai_summary")}
                              </h3>
                              <ul className="check-list">
                                {finalSummary?.skillSummary?.finalSummary?.map((item: string, index) => {
                                  return (
                                    <li key={index}>
                                      <CheckSecondaryIcon className="me-2" /> {item}
                                    </li>
                                  );
                                })}
                              </ul>
                            </div>
                          )}
                          <div className="col-md-3">
                            <AIVerifiedIcon />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="row g-4">
                    {finalSummary && finalSummary.behaviouralScores && Object.keys(finalSummary.behaviouralScores).length !== 0 ? (
                      <>
                        <div className="col-12 col-md-6 d-flex">
                          <div className="skills-score-card skills-graph-card flex-grow-1">
                            {<Radar data={radarChartData} options={{ maintainAspectRatio: false }} />}
                          </div>
                        </div>
                      </>
                    ) : null}

                    {skillSpecificAssessment && skillSpecificAssessment?.skillsScores?.length > 0 ? (
                      <div className="col-12 col-md-6 d-flex">
                        <div className="skills-score-card skills-graph-card flex-grow-1">
                          <Bar
                            data={barChartData}
                            options={{
                              maintainAspectRatio: false,
                              plugins: { legend: { display: false }, title: { display: false } },
                              scales: { x: { grid: { display: false } } },
                            }}
                          />
                        </div>
                      </div>
                    ) : null}
                  </div>
                  {/* Skills Cards Section */}
                  <div className="row g-4">
                    {/* Left Skills Card */}
                    {skillSpecificAssessment &&
                    (skillSpecificAssessment?.skillsScores?.length > 0 || skillSpecificAssessment?.careerBasedSkillsScore > 0) ? (
                      <div className="col-lg-4">
                        <div className="summary-text-card skills-score-card">
                          <h3 className="sub-tittle mt-0">{t("skills_score")}</h3>
                          <ul className="skills-list">
                            <li className="skills-item">
                              <span className="skill-name">{t("career_based_skills")}</span>
                              <span className="skill-rating">
                                <StarIcon /> {skillSpecificAssessment.careerBasedSkillsScore}/10
                              </span>
                            </li>
                            {skillSpecificAssessment.skillsScores.map((item, index) => (
                              <li key={index} className="skills-item">
                                <span className="skill-name">{item.skill_name}</span>
                                <span className="skill-rating">
                                  {item.skill_marks === 10 ? (
                                    <span className="skill-badge">{t("extreme")}</span>
                                  ) : (
                                    <>
                                      <StarIcon /> {item.skill_marks}/10
                                    </>
                                  )}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ) : null}

                    {/* Right Skills Summary */}
                    {skillSpecificAssessment && skillSpecificAssessment?.skillsScores?.length > 0 ? (
                      <div className="col-lg-8">
                        <div className="summary-text-card skills-summary-card">
                          <h3 className="sub-tittle mt-0">{translate("skills_summary")}</h3>
                          <div className="skills-tags">
                            {skillSpecificAssessment.skillsScores.map((skill, index) => (
                              <span
                                key={index}
                                className={`skill-tag ${activeSkillTab === skill.skill_name ? "active" : ""}`}
                                onClick={() => setActiveSkillTab(skill.skill_name)}
                              >
                                {skill.skill_name}
                              </span>
                            ))}
                          </div>
                          <div className="strengths-gaps">{renderSkillDetails()}</div>
                        </div>
                      </div>
                    ) : null}

                    {/* Improvement Areas */}
                    <div className="col-12">
                      {finalSummary &&
                      finalSummary.developmentRecommendations &&
                      finalSummary?.developmentRecommendations?.recommendations?.length > 0 ? (
                        <div className="summary-text-card improvement-areas-card">
                          <h3 className="sub-tittle mt-0">{translate("improvement_areas")}</h3>
                          <div className="row g-4">
                            {finalSummary.developmentRecommendations.recommendations.map((recommendation, index) => (
                              <div key={index} className="col-md-4">
                                <div className="improvement-card h-100">
                                  <h4 className="title">{recommendation.title}</h4>
                                  <p className="description">{recommendation.description}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </div>
              ) : (
                // No data available - show message
                <div className="no-final-assessment d-flex align-items-center justify-content-center">
                  <NoDataFoundIcon width={300} height={300} />
                </div>
              ))}
            {!selectedTab &&
              (isLoadingInterviewHistory ? (
                <div className="history-content">
                  <div className="interview-summary">
                    <div className="summary-header">
                      <Skeleton height={21} width={"20%"} borderRadius={6} />
                    </div>
                    <div className="interviewer">
                      <Skeleton height={21} width={"20%"} className="mb-4" borderRadius={6} />

                      <div className="interviewer-info">
                        <Skeleton height={45} width={45} borderRadius={100} />
                        <Skeleton height={18} width={100} borderRadius={6} />
                      </div>
                    </div>
                    <div className="summary-scores">
                      <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                      <div className="score-btns mt-2">
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                        <Skeleton height={45} width={170} className="mb-4" borderRadius={12} />
                      </div>
                    </div>
                    <div className="summary-highlights">
                      <Skeleton height={18} width={100} className="mb-4" borderRadius={6} />
                      <ul className="highlight-list p-0">
                        <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                      </ul>
                    </div>
                  </div>
                  <div className="interview-summary">
                    <div className="summary-header">
                      <Skeleton height={21} width={"20%"} borderRadius={6} />
                    </div>
                    <div className="interviewer">
                      <div className="interviewer-info large">
                        <Skeleton height={45} width={45} borderRadius={100} />
                        <Skeleton height={18} width={100} borderRadius={6} />
                      </div>
                    </div>
                    <div className="summary-highlights">
                      <Skeleton height={18} width={100} borderRadius={6} className="mb-4" />
                      <ul className="highlight-list p-0">
                        <Skeleton height={16} width={"80%"} className="mb-3" borderRadius={6} count={4} />
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="history-content">
                  {candidateInterviewHistory && candidateInterviewHistory.length > 0 ? (
                    <>
                      {candidateInterviewHistory.map((historyItem) => (
                        <>
                          <div key={historyItem.interviewerId} className="interview-summary">
                            <div className="summary-header">
                              <h1 className="summary-heading">
                                {t("round")} {historyItem.roundNumber} {t("summary")}
                              </h1>
                            </div>
                            <div className="interviewer">
                              <h2 className="summary-title">{t("interview_by")}</h2>
                              <div className="interviewer-info large">
                                <Image
                                  src={historyItem.interviewerImage || noImageFound}
                                  alt={t("interviewer_avatar")}
                                  className="interviewer-avatar"
                                  width={50}
                                  height={50}
                                />
                                <div>
                                  <span className="interviewer-name">{historyItem.interviewerName}</span>
                                  {historyItem.endTime ? (
                                    <div className="text-muted small fs-5">
                                      {new Date(historyItem.endTime).toLocaleString(undefined, {
                                        year: "numeric",
                                        month: "short",
                                        day: "numeric",
                                        hour: "2-digit",
                                        minute: "2-digit",
                                      })}
                                    </div>
                                  ) : null}
                                </div>
                              </div>
                            </div>
                            <div className="summary-scores">
                              <h2 className="summary-title">{t("scores")}</h2>
                              <div className="score-btns">
                                <Button className="secondary-btn rounded-md px-3 py-3">
                                  {t("hard_skills")} : {historyItem.hardSkillMarks}
                                </Button>
                                {Object.entries(historyItem.skillScores).map(([skillName, score]) => (
                                  <Button key={skillName} className="secondary-btn rounded-md px-3 py-3">
                                    {skillName} : {score === 10 ? t("extreme") : score}
                                  </Button>
                                ))}
                              </div>
                            </div>
                            {historyItem.interviewSummary?.highlight?.length > 0 ? (
                              <div className="summary-highlights">
                                <h2 className="summary-title">{t("highlights")}</h2>
                                <ul className="highlight-list">
                                  {historyItem.interviewSummary?.highlight.map((item: string, index) => {
                                    return (
                                      <li key={index} className="highlight-item">
                                        {item}
                                      </li>
                                    );
                                  })}
                                </ul>
                              </div>
                            ) : null}
                          </div>
                          {/* Show interviewer Feedback */}
                          {historyItem.interviewerPerformanceAiAnalysis && historyItem.interviewerPerformanceAiAnalysis.highlights?.length ? (
                            <div className="interview-summary">
                              <div className="summary-header">
                                <h1 className="summary-heading">{t("interviewer_performance_feedback")}</h1>
                              </div>
                              {/* <div key={`${historyItem.interviewerId}-${historyItem.roundNumber}`} className="interview-summary"> */}
                              <div className="interviewer">
                                <div className="interviewer-info large">
                                  <Image
                                    src={historyItem.interviewerImage || noImageFound}
                                    alt={t("interviewer_avatar")}
                                    className="interviewer-avatar"
                                    width={50}
                                    height={50}
                                  />
                                  <span className="interviewer-name">{historyItem.interviewerName}</span>
                                </div>
                              </div>
                              <div className="summary-highlights">
                                <h2 className="summary-title">{t("highlights")}</h2>
                                <ul className="highlight-list">
                                  {historyItem.interviewerPerformanceAiAnalysis.highlights.map((item: string, highlightIndex: number) => (
                                    <li key={highlightIndex} className="highlight-item">
                                      {item}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          ) : // </div>
                          null}
                        </>
                      ))}
                      {/* Show current user's performance feedback */}
                      {/* {aiInterviewerAnalysis && (
                        <div className="interview-summary">
                          <div className="summary-header">
                            <h1 className="summary-heading">{t("your_performance_feedback")}</h1>
                          </div>
                          <div className="interviewer">
                            <div className="interviewer-info large">
                              <Image
                                src={authData?.image || noImageFound}
                                alt={t("interviewer_avatar")}
                                className="interviewer-avatar"
                                width={50}
                                height={50}
                              />
                              <span className="interviewer-name">
                                {authData?.first_name} {authData?.last_name}
                              </span>
                            </div>
                          </div>
                          <div className="summary-highlights">
                            <h2 className="summary-title">{t("highlights")}</h2>
                            <ul className="highlight-list">
                              {aiInterviewerAnalysis.highlights?.map((item: string, index: number) => {
                                return (
                                  <li key={index} className="highlight-item">
                                    {item}
                                  </li>
                                );
                              })}
                            </ul>
                          </div>
                        </div>
                      )} */}

                      {/* Show all interviewer performance feedback for admin users */}
                      {/* {allInterviewerAnalysis.length > 0 && (
                        <div className="admin-interviewer-analysis">
                          <div className="summary-header">
                            <h1 className="summary-heading">{t("all_interviewer_performance_feedback")}</h1>
                          </div>
                          {allInterviewerAnalysis.map((analysisData) => (
                            <div key={`${analysisData.interviewerId}-${analysisData.roundNumber}`} className="interview-summary">
                              <div className="interviewer">
                                <div className="interviewer-info large">
                                  <Image
                                    src={analysisData.interviewerImage || noImageFound}
                                    alt={t("interviewer_avatar")}
                                    className="interviewer-avatar"
                                    width={50}
                                    height={50}
                                  />
                                  <span className="interviewer-name">
                                    {analysisData.interviewerName} - Round {analysisData.roundNumber}
                                  </span>
                                </div>
                              </div>
                              {analysisData.analysis?.highlights && (
                                <div className="summary-highlights">
                                  <h2 className="summary-title">{t("highlights")}</h2>
                                  <ul className="highlight-list">
                                    {analysisData.analysis.highlights.map((item: string, highlightIndex: number) => (
                                      <li key={highlightIndex} className="highlight-item">
                                        {item}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )} */}
                    </>
                  ) : (
                    <div className="no-interview-history d-flex align-items-center justify-content-center">
                      <NoDataFoundIcon width={300} height={300} />
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      </div>
    </>
  );
};
export default CandidateProfile;
