/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import "swiper/css";
// import "swiper/css/navigation";
// import "swiper/css/pagination";

import {
  LocalUser,
  RemoteUser,
  useJoin,
  useLocalCameraTrack,
  useLocalMicrophoneTrack,
  usePublish,
  useRemoteUsers,
  useRTCClient,
} from "agora-rtc-react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { yupResolver } from "@hookform/resolvers/yup";
import { getSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { io, Socket } from "socket.io-client";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import Image from "next/image";

import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textarea from "@/components/formElements/Textarea";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import LetterFoldIcon from "@/components/svgComponents/LetterFoldIcon";
import MicIcon from "@/components/svgComponents/MicIcon";
import VideoCallIcon from "@/components/svgComponents/VideoCallIcon";
import allSkillInterviewed from "../../../../public/assets/images/all-skills-interviewed.svg";

import {
  AGORA_AUDIO_CONSTRAINTS,
  AGORA_USER_TYPE,
  FOLLOW_UP_TYPE,
  INTERVIEW_QUESTION_TYPE,
  QUESTION_TYPES,
  SOCKET_ROUTES,
} from "@/constants/commonConstants";
import { AgoraTokenData } from "@/interfaces/agoraInterfaces";
import { getAgoraToken, startRecording, stopRecording } from "@/services/agoraService";
import style from "../../../styles/conductInterview.module.scss";
import { decryptInfo, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import {
  IAddFollowUpSkillResponse,
  IAddInterviewSkillQuestion,
  IInterviewQuestionFormValues,
  IInterviewQuestionResponse,
} from "@/interfaces/interviewInterfaces";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import ProgressTracker from "./ProgressTracker";
import EndInterViewModal from "@/components/commonModals/EndInterViewModal";
import ROUTES from "@/constants/routes";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import { IParsedInfo, ISession } from "@/interfaces/commonInterfaces";
import { AuthState } from "@/redux/slices/authSlice";
import { IInterviewState, updateQuestionAnswer, addFollowupQuestion, addFollowupSkill } from "@/redux/slices/interviewSlice";
import { updateInterviewAnswers, endInterview, addInterviewSkillQuestion } from "@/services/interviewServices";
import { addAnswerValidation } from "@/validations/interviewValidations";
import FollowUpModal from "@/components/commonModals/FollowUpModal";
import { useTranslate } from "@/utils/translationUtils";

const Interview = () => {
  const router = useRouter();
  const t = useTranslations();
  const translate = useTranslate();
  const dispatch = useDispatch();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const params = useSearchParams();
  const info = params?.get("info");
  const [parsedInfo, setParsedInfo] = useState<IParsedInfo | null>(null);
  console.log("info========>>>>>>>>>>>>>> Interview", info);
  console.log("parsedInfo========>>>>>>>>>>>>>> Interview", parsedInfo);

  useEffect(() => {
    toast.dismiss();
    try {
      if (info) {
        const decryptedInfo = decryptInfo(info);
        if (decryptedInfo) {
          const parsed = JSON.parse(decryptedInfo);
          if (!parsed?.isAuthorized) {
            toastMessageError(t("unauthorized_access"));
            router.replace(ROUTES.INTERVIEW.CALENDAR);
          }
          setParsedInfo(parsed);
        } else {
          moveToCalendar();
        }
      }
    } catch (error) {
      console.log("error", error);
      moveToCalendar();
    }
  }, [info]);

  const moveToCalendar = () => {
    toastMessageError(t("invalid_or_malformed_url_parameters"));
    router.replace(ROUTES.INTERVIEW.CALENDAR);
  };

  // Only proceed with the rest of the component if parsedInfo is available
  const interviewId = parsedInfo ? +parsedInfo.interviewId : 0;
  const resumeLink = parsedInfo ? parsedInfo.resumeLink : "";
  const jobApplicationId = parsedInfo ? parsedInfo.jobApplicationId : 0;
  const jobId = parsedInfo ? parsedInfo.jobId : 0;
  const channelName = parsedInfo ? parsedInfo.channelName : "";
  const candidateName = parsedInfo ? parsedInfo.candidateName : "";
  const interviewerName = parsedInfo ? parsedInfo.interviewerName : "";

  const [areAllSkillsInterviewed, setAreAllSkillsInterviewed] = useState(false);
  const [tokenData, setTokenData] = useState<AgoraTokenData | null>(null);
  const [joining, setJoining] = useState(false);
  // const [lowAudioWarning, setLowAudioWarning] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [stopRecordingData, setStopRecordingData] = useState({
    resourceId: "",
    sid: "",
    uid: "",
    mode: "",
    channelName: "",
  });

  const joinOptions = tokenData
    ? {
        appid: process.env.NEXT_PUBLIC_AGORA_APP_ID!,
        channel: tokenData.channelName,
        token: tokenData.token,
        uid: tokenData.uid,
      }
    : { appid: "", channel: "", token: "", uid: "" };

  const { localMicrophoneTrack } = useLocalMicrophoneTrack(isMicOn, AGORA_AUDIO_CONSTRAINTS);
  const { localCameraTrack } = useLocalCameraTrack(isCameraOn);
  useJoin(joinOptions, joining && !!tokenData);
  usePublish([localMicrophoneTrack, localCameraTrack]);

  const remoteUsers = useRemoteUsers();
  const client = useRTCClient();

  console.log("client#############=======>>>>>interviwer", client);
  console.log("remoteUsers#########=====>>>>>interviewer", remoteUsers);
  console.log("joinOptions######", joinOptions);

  // Fetch token for the interviewer
  const fetchInterviewerToken = useCallback(
    async (interviewId: string) => {
      try {
        if (!channelName || !authData?.id) return;
        const response = await getAgoraToken({
          interviewId,
          personType: AGORA_USER_TYPE.Interviewer,
          channelName,
          interviewerId: authData?.id.toString(),
        });
        if (response?.data?.data) {
          setTokenData(response.data.data);
          setJoining(true);
        } else {
          toastMessageError(t(response?.data?.message));
          router.replace(ROUTES.INTERVIEW.CALENDAR);
        }
      } catch (error) {
        console.error("Error fetching token:", error);
      }
    },
    [channelName, authData?.id]
  );

  // Initialize the interview when component loads
  useEffect(() => {
    const initInterview = async () => {
      try {
        if (interviewId) {
          await fetchInterviewerToken(interviewId.toString());
        }
      } catch (error) {
        console.error("Error initializing interview:", error);
      }
    };

    initInterview();
  }, [interviewId, channelName, authData?.id]);

  // Disable picture-in-picture for all video elements
  useEffect(() => {
    const disablePictureInPicture = (video: HTMLVideoElement) => {
      // Set the disablePictureInPicture property
      video.disablePictureInPicture = true;

      // Set controlsList attribute to disable picture-in-picture
      video.setAttribute("controlsList", "nofullscreen nodownload noremoteplayback nopictureinpicture");
      video.setAttribute("disablePictureInPicture", "true");

      // Prevent picture-in-picture events
      const preventPiP = (e: Event) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
      };

      video.addEventListener("enterpictureinpicture", preventPiP, true);
      video.addEventListener("leavepictureinpicture", preventPiP, true);
    };

    const processAllVideos = () => {
      const videos = document.querySelectorAll("video");
      videos.forEach((video) => disablePictureInPicture(video as HTMLVideoElement));
    };

    // Create a MutationObserver to watch for new video elements
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.tagName === "VIDEO") {
              disablePictureInPicture(element as HTMLVideoElement);
            } else {
              const videos = element.querySelectorAll("video");
              videos.forEach((video) => disablePictureInPicture(video as HTMLVideoElement));
            }
          }
        });
      });
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Process existing videos
    processAllVideos();

    // Also run periodically as a fallback
    const interval = setInterval(processAllVideos, 1000);

    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  // remove media and streams when user clicks back/forward button
  useEffect(() => {
    const handlePopState = () => {
      console.log("handlePopState ****");
      removeMediaAndStreams();
    };

    window.addEventListener("popstate", handlePopState);
    return () => window.removeEventListener("popstate", handlePopState);
  }, []);

  // Toggle camera
  const toggleCamera = () => {
    setIsCameraOn(!isCameraOn);
  };

  // Toggle microphone
  const toggleMic = () => {
    setIsMicOn(!isMicOn);
  };

  // End the call
  const endCall = async () => {
    setJoining(false);

    // Explicitly turn off camera and mic states
    setIsCameraOn(false);
    setIsMicOn(false);

    // Release camera track
    if (localCameraTrack) {
      try {
        localCameraTrack.setEnabled(false);
        localCameraTrack.close();
      } catch (error) {
        console.error("Error closing camera track:", error);
      }
    }

    // Release microphone track
    if (localMicrophoneTrack) {
      try {
        localMicrophoneTrack.setEnabled(false);
        localMicrophoneTrack.close();
      } catch (error) {
        console.error("Error closing microphone track:", error);
      }
    }

    // Leave Agora client
    if (client) {
      try {
        await client.leave();
      } catch (error) {
        console.error("Error leaving client:", error);
      }
    }

    // Clean up socket connection if active
    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.disconnect();
    }

    removeMediaAndStreams();

    // Navigate away from the interview
    router.replace(ROUTES.HOME);
  };

  const handleEndCall = async () => {
    try {
      await stopTranscription();
      await endCall();
      if (isRecording) await handleStopRecording();
    } catch (error) {
      console.error("Error ending call:", error);
    }
  };

  const handleStopRecording = async () => {
    try {
      await stopRecording(stopRecordingData);
    } catch (error) {
      console.error("Error stopping recording:", error);
    }
  };

  const handleStartRecording = async () => {
    if (!tokenData) return;
    try {
      const recordingData = {
        channelName: tokenData.channelName,
        interviewId: interviewId.toString(), // Replace with actual interview ID if needed
      };
      const response = await startRecording(recordingData);
      if (response?.data?.data) {
        setStopRecordingData(response.data.data);
      }
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  // ------------------------------------Interview Questions functionality----------------------------------------------------------;

  const interviewQuestionsData = useSelector((state: { interview: IInterviewState }) => state.interview);
  const interviewStaticInformation = interviewQuestionsData.interviewStaticInformation;

  console.log("interviewStaticInformation====", interviewStaticInformation);

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    watch,
    formState: { errors },
  } = useForm<IInterviewQuestionFormValues | any>({ mode: "onChange", resolver: yupResolver(addAnswerValidation(translate)) });

  const socketRef = useRef<Socket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const elapsedTimeRef = useRef<{ time: number; type: string; jobId: number; jobSkillId: number }>({
    time: 0,
    type: "",
    jobId,
    jobSkillId: 0,
  });

  const [isRecording, setIsRecording] = useState(false);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  const [followUpQuestion, setFollowUpQuestion] = useState("");
  const [followUpSkill, setFollowUpSkill] = useState({
    skillType: "",
    skillTitle: "",
    skillId: undefined,
    questions: [],
  });
  const [openQuestions, setOpenQuestions] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [followupLoading, setFollowupLoading] = useState(false);
  const [showEndInterviewModal, setShowEndInterviewModal] = useState(false);

  console.log("elapsedTime======", elapsedTime);
  console.log("openQuestions=====", openQuestions);

  const [selectedTab, setSelectedTab] = useState<string>(QUESTION_TYPES.CAREER_BASED);
  const [selectedSkill, setSelectedSkill] = useState({ skillId: 0, skillName: "" });
  const [skillMarked, setSkillMarked] = useState(0);
  const [isError, setIsError] = useState(false);

  const [currentCultureSkillIndex, setCurrentCultureSkillIndex] = useState(0);
  const [currentRoleSkillIndex, setCurrentRoleSkillIndex] = useState(0);

  const careerBasedQuestions = useMemo(
    () => interviewQuestionsData?.interviews?.[interviewId]?.careerBasedQuestions || { questions: [], score: 0 },
    [interviewQuestionsData, interviewId]
  );
  const roleSpecificQuestions = useMemo(
    () => interviewQuestionsData?.interviews?.[interviewId]?.roleSpecificQuestions || {},
    [interviewQuestionsData, interviewId]
  );
  const cultureSpecificQuestions = useMemo(
    () => interviewQuestionsData?.interviews?.[interviewId]?.cultureSpecificQuestions || {},
    [interviewQuestionsData, interviewId]
  );

  useEffect(() => {
    console.log("inside callback==========>>>>>");
    elapsedTimeRef.current.jobSkillId = selectedSkill.skillId;
    elapsedTimeRef.current.type = selectedTab;
    elapsedTimeRef.current.jobId = jobId;
  }, [selectedTab, jobId, selectedSkill]);

  const roleSkills = useMemo(() => Object.keys(roleSpecificQuestions), [roleSpecificQuestions]);

  const cultureSkills = useMemo(() => Object.keys(cultureSpecificQuestions), [cultureSpecificQuestions]);

  console.log("===========errors", control._formState.errors);

  console.log("@@@@@@@@@@@@====selectedSkill", selectedSkill);
  console.log("@@@@@@@@@@@@====skillMarked", skillMarked);

  console.log("========elapsedTimeRef", elapsedTimeRef.current);

  console.log("====refollowUpQuestion", followUpQuestion);

  // Add timer functionality when recording starts/stops
  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null;

    if (isRecording) {
      // Start the timer when recording begins
      timerId = setInterval(() => {
        setElapsedTime((prevTime) => {
          const newTime = prevTime + 1;
          elapsedTimeRef.current.time = newTime;
          return newTime;
        });
      }, 1000);
    }

    return () => {
      if (timerId) clearInterval(timerId);
    };
  }, [isRecording]);

  // to prefill data for career based skill
  useEffect(() => {
    if (careerBasedQuestions?.questions) {
      prefillFormData(careerBasedQuestions.questions);
      setSkillMarked(careerBasedQuestions.score || 0);
    }
  }, [careerBasedQuestions]);

  // to setup socket connection on component mount
  useEffect(() => {
    startSocketConnection();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      removeMediaAndStreams();
    };
  }, [interviewId, jobApplicationId]);

  // send message to server every 10 seconds if recording is not started to backend for redis handling multiple joins at a time
  useEffect(() => {
    console.log("isRecording !!!!!!!!!!", isRecording);

    let interval: NodeJS.Timeout;

    if (!isRecording) {
      console.log("Inside setInterval !!!!!!!!!", isRecording);
      interval = setInterval(() => {
        if (socketRef.current) {
          socketRef.current.emit("message", {});
        }
      }, 10000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRecording]);

  // Function to initialize socket with the token
  const initializeSocket = (token: string) => {
    // Initialize the socket with the new token
    socketRef.current = io(process.env.NEXT_PUBLIC_SOCKET_CONNECTION_URL!, {
      path: SOCKET_ROUTES.CONDUCT_INTERVIEW,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
      query: {
        interviewId: interviewId.toString(),
        jobApplicationId: jobApplicationId.toString(),
      },
    });

    // Add socket event listeners
    socketRef.current?.on("connect", () => {
      console.log("Connected to server");
    });

    socketRef.current?.on("disconnect", (reason, details) => {
      console.log("Disconnected from server:", reason, details);
    });

    socketRef.current?.on("connect_error", (error) => {
      console.log("Connect error:", error);
    });
  };

  const startSocketConnection = async () => {
    const session = await getSession();
    const parsedSession = { ...session } as ISession;
    const token = parsedSession?.user?.data?.token;
    console.log("token", token);
    if (token && !socketRef.current?.connected && interviewId && jobApplicationId) {
      initializeSocket(token);
    }
  };

  // function to handle the questions for answer filling
  const handleOpen = (questionId: number) => {
    setOpenQuestions((prev) => {
      // If the question is already open, close it
      if (prev.includes(questionId)) {
        return prev.filter((id) => id !== questionId);
      }
      // Otherwise, add it to the open questions array
      return [...prev, questionId];
    });
  };

  // function to handle microphone and audio buffer
  async function openMicrophone(microphone: MediaRecorder, socket: Socket | null) {
    console.log("microphone======", microphone);
    return new Promise<void>((resolve) => {
      let buffer: Blob[] = [];
      const bufferInterval = 5000;
      microphone.onstart = () => {
        resolve();
      };

      microphone.onstop = () => {
        if (buffer.length > 0 && socket?.connected) {
          const finalBlob = new Blob(buffer, { type: "audio/webm" });
          socket.emit(
            "message",
            {
              blob: finalBlob,
              time: elapsedTimeRef.current.time,
              jobSkillId: elapsedTimeRef.current.jobSkillId,
              type: elapsedTimeRef.current.type,
              jobId: elapsedTimeRef.current.jobId,
            },
            (response: any) => {
              if (response.success) {
                console.log("====response onstop", response);
                // Store follow-up question for the currently open question (first one in the array)
                if (response.success && response.data) {
                  if (typeof response.data === "string") {
                    setFollowUpQuestion(response.data);
                  } else {
                    setFollowUpSkill(response.data);
                  }
                }
              }
            }
          );
          buffer = [];
        }
      };

      microphone.ondataavailable = (event: BlobEvent) => {
        console.log("event======", event);
        if (event.data.size > 0) {
          buffer.push(event.data);
        }
      };

      const sendInterval = setInterval(() => {
        if (buffer.length > 0 && socket?.connected) {
          // Calculate buffer size in KB
          const totalSizeBytes = buffer.reduce((total, blob) => total + blob.size, 0);
          const totalSizeKB = (totalSizeBytes / 1024).toFixed(2);
          console.log(`Buffer size before sending: ${totalSizeKB} KB`);

          const audioBlob = new Blob(buffer, { type: "audio/webm" });
          socket.emit(
            "message",
            {
              blob: audioBlob,
              time: elapsedTimeRef.current.time,
              jobSkillId: elapsedTimeRef.current.jobSkillId,
              type: elapsedTimeRef.current.type,
              jobId: elapsedTimeRef.current.jobId,
            },
            (response: any) => {
              if (response.success) {
                console.log("====response", response);
                // Store follow-up question or follow-up skill
                if (response.success && response.data) {
                  if (typeof response.data === "string") {
                    setFollowUpQuestion(response.data);
                  } else {
                    setFollowUpSkill(response.data);
                  }
                }
              }
            }
          );
          buffer = [];
        }
      }, bufferInterval);

      microphone.start(500);

      console.log("buffer======", buffer);

      const cleanup = () => {
        clearInterval(sendInterval);
        if (microphone.state !== "inactive") {
          microphone.stop();
        }
      };
      socket?.on("disconnect", cleanup);
      socket?.on("error", cleanup);
    });
  }

  // function to start voice to text transcriptions
  const startTranscription = useCallback(async () => {
    try {
      setIsRecording(true);
      await startSocketConnection();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log("stream======", stream);
      streamRef.current = stream;
      const options = { mimeType: "audio/webm" };
      const recorder = new MediaRecorder(stream, options);
      console.log("recorder======", recorder);
      mediaRecorderRef.current = recorder;
      await openMicrophone(recorder, socketRef.current);
    } catch (error) {
      console.error("Error starting transcription:", error);
    }
  }, []);

  // function to stop voice to text transcriptions
  const stopTranscription = useCallback(async () => {
    setIsRecording(false);
  }, []);

  console.log("mediaRecorderRef.current ****", mediaRecorderRef.current);
  console.log("streamRef.current ****", streamRef.current);

  const removeMediaAndStreams = () => {
    console.log(streamRef.current, "removeMediaAndStreams ****", mediaRecorderRef.current);
    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current?.state !== "inactive") {
        mediaRecorderRef.current?.stop();
      }
      mediaRecorderRef.current = null;
    }
    if (streamRef.current) {
      console.log(streamRef.current.getTracks(), "streamRef.current.getTracks() ****");
      streamRef.current?.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      streamRef.current = null;
    }
  };

  const handleNextLockedSkills = () => {
    toast.dismiss();
    if (areAllSkillsLocked(selectedTab === QUESTION_TYPES.CAREER_BASED ? QUESTION_TYPES.ROLE_SPECIFIC : QUESTION_TYPES.CULTURE_SPECIFIC)) {
      // state update
      setAreAllSkillsInterviewed(true);
      return true;
    }
    setAreAllSkillsInterviewed(false);
    return false;
  };

  const handleSaveAndNext = async (data: IInterviewQuestionFormValues) => {
    try {
      console.log("======>>>>>>>", skillMarked);
      if (skillMarked === 0) {
        console.log("inside handleSaveAndNext");
        setIsError(true);
        // toastMessageError(t("please_mark_stratum_score"));
        return;
      }

      setLoading(true);
      const { behavioralInfo, ...rest } = data;

      console.log("==========behavioralInfo", behavioralInfo);

      // Transform the data into the required format
      const answers = Object.entries(rest).map(([key, value]) => {
        // Extract the ID from the key (e.g., "answer--->>148" -> 148)
        const questionId = parseInt(key.replace("answer-", ""));

        return {
          questionId,
          answer: value,
        };
      });

      let skillId;
      let jobSkillId;

      if (selectedTab === QUESTION_TYPES.ROLE_SPECIFIC) {
        skillId = roleSpecificQuestions[selectedSkill.skillName].questions[0].skillId;
        jobSkillId = roleSpecificQuestions[selectedSkill.skillName].questions[0].jobSkillId;
      } else if (selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC) {
        skillId = cultureSpecificQuestions[selectedSkill.skillName].questions[0].skillId;
        jobSkillId = cultureSpecificQuestions[selectedSkill.skillName].questions[0].jobSkillId;
      }

      const payload = {
        interviewId: interviewId,
        skillId,
        jobSkillId,
        skillMarked: skillMarked,
        skillType: selectedTab,
        answers,
      };

      console.log("==========payload", payload);

      // After saving, handle navigation based on current section and skill
      const response = await updateInterviewAnswers(payload);

      const interviewerName = `${authData?.first_name.charAt(0).toUpperCase()}${authData?.last_name.charAt(0).toUpperCase()}`;
      console.log("interviewerName", interviewerName);

      console.log("api response====", response);

      if (response?.data?.success) {
        console.log("interviewerName", interviewerName);
        switch (selectedTab) {
          case QUESTION_TYPES.CAREER_BASED:
            dispatch(
              updateQuestionAnswer({
                interviewId,
                questionType: selectedTab,
                questionAnswers: answers,
                stratumScore: skillMarked,
              })
            );
            break;
          case QUESTION_TYPES.ROLE_SPECIFIC:
            dispatch(
              updateQuestionAnswer({
                interviewId,
                questionType: selectedTab,
                category: selectedSkill.skillName,
                questionAnswers: answers,
                stratumScore: skillMarked,
                interviewerName,
              })
            );
            break;
          case QUESTION_TYPES.CULTURE_SPECIFIC:
            dispatch(
              updateQuestionAnswer({
                interviewId,
                questionType: selectedTab,
                category: selectedSkill.skillName,
                questionAnswers: answers,
                stratumScore: skillMarked,
                interviewerName,
              })
            );
            break;
        }
      }

      setTimeout(() => {
        handleNextLockedSkills();
        handleNextSkillInterview();
      }, 100);
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleEndInterview = async () => {
    try {
      setLoader(true);
      const response = await endInterview({ interviewId, behaviouralNotes: getValues("behavioralInfo") ?? "" });
      handleEndCall();

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
        router.push(ROUTES.INTERVIEW.CALENDAR);
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoader(false);
    }
  };

  // function to handle backward skill navigation
  const handlePreviousSkillInterview = () => {
    if (
      selectedTab !== QUESTION_TYPES.ROLE_SPECIFIC &&
      areAllSkillsLocked(selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC ? QUESTION_TYPES.ROLE_SPECIFIC : QUESTION_TYPES.CAREER_BASED)
    ) {
      setAreAllSkillsInterviewed(true);
    } else {
      setAreAllSkillsInterviewed(false);
    }
    setOpenQuestions([]);
    setFollowUpQuestion("");
    setFollowUpSkill({
      skillType: "",
      skillTitle: "",
      questions: [],
      skillId: undefined,
    });
    const behavioralInfo = getValues("behavioralInfo");
    reset({
      behavioralInfo: behavioralInfo,
    });
    setIsError(false);

    switch (selectedTab) {
      case QUESTION_TYPES.ROLE_SPECIFIC:
        setSelectedTab(QUESTION_TYPES.CAREER_BASED);
        prefillFormData(careerBasedQuestions.questions || []);
        setSkillMarked(careerBasedQuestions.score || 0);
        setSelectedSkill({ skillId: 0, skillName: "" });
        break;

      case QUESTION_TYPES.CULTURE_SPECIFIC:
        setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);
        const lastRoleSkill = roleSkills.findLast((skill) => !isSkillLocked(QUESTION_TYPES.ROLE_SPECIFIC, skill));
        if (lastRoleSkill) {
          const roleSkill = roleSpecificQuestions[lastRoleSkill];
          setSelectedSkill({ skillId: roleSkill.questions.length ? roleSkill.questions[0].jobSkillId : 0, skillName: lastRoleSkill });
          setCurrentRoleSkillIndex(roleSkills.length - 1);
          prefillFormData(roleSpecificQuestions[lastRoleSkill].questions || []);
          setSkillMarked(roleSpecificQuestions[lastRoleSkill].score || 0);
        }
        break;

      case QUESTION_TYPES.CAREER_BASED:
        break;
    }
  };

  const areAllSkillsLocked = (skillType: string) => {
    return skillType === QUESTION_TYPES.ROLE_SPECIFIC
      ? Object.values(roleSpecificQuestions).every((skill) => skill.isLocked)
      : skillType === QUESTION_TYPES.CULTURE_SPECIFIC
        ? Object.values(cultureSpecificQuestions).every((skill) => skill.isLocked)
        : false;
  };

  // function to handle forward skill navigation
  const switchCategory = () => {
    handleNextLockedSkills();
    setOpenQuestions([]);
    setFollowUpQuestion("");
    setFollowUpSkill({
      skillType: "",
      skillTitle: "",
      questions: [],
      skillId: undefined,
    });
    const behavioralInfo = getValues("behavioralInfo");
    reset({
      behavioralInfo: behavioralInfo,
    });
    setIsError(false);

    console.log("inside next @@@@@");

    switch (selectedTab) {
      case QUESTION_TYPES.CAREER_BASED:
        // Move from Career-based to Role-specific
        setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);
        const firstUnlockedRoleSkillIndex = roleSkills.findIndex((skill) => !isSkillLocked(QUESTION_TYPES.ROLE_SPECIFIC, skill));
        if (firstUnlockedRoleSkillIndex !== -1) {
          const roleSkill = roleSpecificQuestions[roleSkills[firstUnlockedRoleSkillIndex]];
          setSelectedSkill({
            skillId: roleSkill.questions.length ? roleSkill.questions[0].jobSkillId : 0,
            skillName: roleSkills[firstUnlockedRoleSkillIndex],
          });
          setCurrentRoleSkillIndex(firstUnlockedRoleSkillIndex);
          prefillFormData(roleSkill.questions || []);
          setSkillMarked(roleSkill.score || 0);
        }
        break;
      case QUESTION_TYPES.ROLE_SPECIFIC:
        setSelectedTab(QUESTION_TYPES.CULTURE_SPECIFIC);
        const firstUnlockedCultureSkillIndex = cultureSkills.findIndex((skill) => !isSkillLocked(QUESTION_TYPES.CULTURE_SPECIFIC, skill));
        if (firstUnlockedCultureSkillIndex !== -1) {
          const cultureSkill = cultureSpecificQuestions[cultureSkills[firstUnlockedCultureSkillIndex]];
          setSelectedSkill({
            skillId: cultureSkill.questions.length ? cultureSkill.questions[0].jobSkillId : 0,
            skillName: cultureSkills[firstUnlockedCultureSkillIndex],
          });
          setCurrentCultureSkillIndex(firstUnlockedCultureSkillIndex);
          prefillFormData(cultureSkill.questions || []);
          setSkillMarked(cultureSkill.score || 0);
        }
        break;
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        break;
    }
  };

  // function to handle next skill navigation for saveAndNext function
  const handleNextSkillInterview = () => {
    setOpenQuestions([]);
    setFollowUpQuestion("");
    const behavioralInfo = getValues("behavioralInfo");
    reset({
      behavioralInfo: behavioralInfo,
    });
    setIsError(false);

    switch (selectedTab) {
      case QUESTION_TYPES.CAREER_BASED:
        // Move from Career-based to Role-specific
        setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);
        const firstUnlockedRoleSkillIndex = roleSkills.findIndex((skill) => !isSkillLocked(QUESTION_TYPES.ROLE_SPECIFIC, skill));
        if (firstUnlockedRoleSkillIndex !== -1) {
          const roleSkill = roleSpecificQuestions[roleSkills[firstUnlockedRoleSkillIndex]];
          setSelectedSkill({
            skillId: roleSkill.questions.length ? roleSkill.questions[0].jobSkillId : 0,
            skillName: roleSkills[firstUnlockedRoleSkillIndex],
          });
          setCurrentRoleSkillIndex(firstUnlockedRoleSkillIndex);
          prefillFormData(roleSkill.questions || []);
          setSkillMarked(roleSkill.score || 0);
        }
        break;

      case QUESTION_TYPES.ROLE_SPECIFIC:
        // Check if there are more role skills to navigate to
        const nextRoleSkillIndex = roleSkills.findIndex(
          (_, index) => index > currentRoleSkillIndex && !isSkillLocked(QUESTION_TYPES.ROLE_SPECIFIC, roleSkills[index])
        );

        if (nextRoleSkillIndex !== -1) {
          // Move to next unlocked role skill
          const roleSkill = roleSpecificQuestions[roleSkills[nextRoleSkillIndex]];
          setSelectedSkill({
            skillId: roleSkill.questions.length ? roleSkill.questions[0].jobSkillId : 0,
            skillName: roleSkills[nextRoleSkillIndex],
          });
          setCurrentRoleSkillIndex(nextRoleSkillIndex);
          prefillFormData(roleSkill.questions || []);
          setSkillMarked(roleSkill.score || 0);
        } else {
          // No more role skills, move to culture-specific
          setSelectedTab(QUESTION_TYPES.CULTURE_SPECIFIC);
          const firstUnlockedCultureSkillIndex = cultureSkills.findIndex((skill) => !isSkillLocked(QUESTION_TYPES.CULTURE_SPECIFIC, skill));
          if (firstUnlockedCultureSkillIndex !== -1) {
            const cultureSkill = cultureSpecificQuestions[cultureSkills[firstUnlockedCultureSkillIndex]];
            setSelectedSkill({
              skillId: cultureSkill.questions.length ? cultureSkill.questions[0].jobSkillId : 0,
              skillName: cultureSkills[firstUnlockedCultureSkillIndex],
            });
            setCurrentCultureSkillIndex(firstUnlockedCultureSkillIndex);
            prefillFormData(cultureSkill.questions || []);
            setSkillMarked(cultureSkill.score || 0);
          }
        }
        break;

      case QUESTION_TYPES.CULTURE_SPECIFIC:
        // Check if there are more culture skills to navigate to
        const nextCultureSkillIndex = cultureSkills.findIndex(
          (_, index) => index > currentCultureSkillIndex && !isSkillLocked(QUESTION_TYPES.CULTURE_SPECIFIC, cultureSkills[index])
        );

        if (nextCultureSkillIndex !== -1) {
          // Move to next unlocked culture skill
          const cultureSkill = cultureSpecificQuestions[cultureSkills[nextCultureSkillIndex]];
          setSelectedSkill({
            skillId: cultureSkill.questions.length ? cultureSkill.questions[0].jobSkillId : 0,
            skillName: cultureSkills[nextCultureSkillIndex],
          });
          setCurrentCultureSkillIndex(nextCultureSkillIndex);
          prefillFormData(cultureSkill.questions || []);
          setSkillMarked(cultureSkill.score || 0);
        }
        // If no more culture skills, do nothing (end of interview)
        break;
    }
  };

  const isLastSkillOverall = (): boolean => {
    return selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC;
  };

  // function to check if skill is completed or not
  const isSkillCompleted = (skillType: string, skillName: string): boolean => {
    switch (skillType) {
      case QUESTION_TYPES.CAREER_BASED:
        return (
          careerBasedQuestions?.questions?.some((question) => question.answer !== undefined && question.answer !== "") || !!careerBasedQuestions.score
        );
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return (
          roleSpecificQuestions?.[skillName]?.questions?.some((question) => question.answer !== undefined && question.answer !== "") ||
          !!roleSpecificQuestions[skillName].score
        );
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return (
          cultureSpecificQuestions?.[skillName]?.questions?.some((question) => question.answer !== undefined && question.answer !== "") ||
          !!cultureSpecificQuestions[skillName].score
        );
      default:
        return false;
    }
  };

  // function to check if skill is locked or not
  const isSkillLocked = (skillType: string, skillName: string): boolean => {
    switch (skillType) {
      case QUESTION_TYPES.CAREER_BASED:
        return !!careerBasedQuestions?.isLocked;
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return !!roleSpecificQuestions?.[skillName]?.isLocked;
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return !!cultureSpecificQuestions?.[skillName]?.isLocked;
      default:
        return false;
    }
  };

  const isAdditionalSkill = (skillName: string): boolean => {
    return selectedTab === QUESTION_TYPES.ROLE_SPECIFIC
      ? !!roleSpecificQuestions?.[skillName]?.isAdditionalSkill
      : !!cultureSpecificQuestions?.[skillName]?.isAdditionalSkill;
  };

  // function to handle pre-filled questions and answers data
  const prefillFormData = (questions: IInterviewQuestionResponse[]) => {
    console.log("inside prefillFormData=====>>>>>");
    if (!questions || questions.length === 0) {
      return;
    }

    // For each question, prefill the form if we have data
    questions.forEach((question) => {
      if (question.answer) {
        // Set the form value for this question
        const fieldName = `answer-${question.id}`;
        // Use reset to set the value while preserving other form values
        reset({
          ...control._formValues,
          [fieldName]: question.answer,
        });

        // Open the question card
        if (!openQuestions.includes(question.id)) {
          setOpenQuestions((prev) => [...prev, question.id]);
        }
      }
    });
  };

  // function to handle selected skill data modification
  const handleSkillSelection = (skill: string, selectedCategory: string) => {
    console.log("selectedCategory===@@@@", selectedCategory);
    const skillData = selectedCategory === QUESTION_TYPES.ROLE_SPECIFIC ? roleSpecificQuestions[skill] : cultureSpecificQuestions[skill];

    if (skill !== selectedSkill.skillName) {
      setFollowUpQuestion("");
    }

    console.log("====skillData@@@@@@@@@@@@@@@", skillData);
    if (selectedCategory === QUESTION_TYPES.CAREER_BASED) {
      setSelectedSkill({ skillId: 0, skillName: "" });
    } else {
      setSelectedSkill({ skillId: skillData?.questions[0].jobSkillId, skillName: skill });
    }
    setIsError(false);

    // update additionalInfo field
    reset({
      ...control._formValues,
      behavioralInfo: getValues("behavioralInfo"),
    });

    // Update the corresponding index tracker based on the selected category
    switch (selectedCategory) {
      case QUESTION_TYPES.ROLE_SPECIFIC:
        if (!isSkillLocked(selectedCategory, skill)) {
          const roleIndex = roleSkills.findIndex((s) => s === skill);
          if (roleIndex !== -1) {
            setCurrentRoleSkillIndex(roleIndex);
          }
          // Prefill form data for role-specific skills
          prefillFormData(roleSpecificQuestions[skill].questions || []);
          setSkillMarked(roleSpecificQuestions[skill].score || 0);
        }
        break;

      case QUESTION_TYPES.CULTURE_SPECIFIC:
        if (!isSkillLocked(selectedCategory, skill)) {
          const cultureIndex = cultureSkills.findIndex((s) => s === skill);
          if (cultureIndex !== -1) {
            setCurrentCultureSkillIndex(cultureIndex);
          }
          // Prefill form data for culture-specific skills
          prefillFormData(cultureSpecificQuestions[skill].questions || []);
          setSkillMarked(cultureSpecificQuestions[skill].score || 0);
        }
        break;

      case QUESTION_TYPES.CAREER_BASED:
        // Prefill form data for career-based skills
        prefillFormData(careerBasedQuestions.questions || []);
        setSkillMarked(careerBasedQuestions.score || 0);
        break;

      default:
        break;
    }
  };

  const renderQuestion = (question: IInterviewQuestionResponse, index: number) => (
    <div key={question.id} className="interview-question-card ">
      <div onClick={() => handleOpen(question.id)}>
        <p className="tittle">
          {t("question")} {index < 9 ? `0${index + 1}` : index + 1} <ArrowDownIcon className={openQuestions.includes(question.id) ? "" : "rotate"} />
        </p>
        <h5>{question.question}</h5>
      </div>
      {openQuestions.includes(question.id) ? (
        <div className="question-body ">
          <InputWrapper>
            <InputWrapper.Label htmlFor={`answer-${question.id}`}>{t("your_notes")}</InputWrapper.Label>
            <div className="custom-textarea">
              <Textarea rows={3} name={`answer-${question.id}`} control={control} placeholder={t("additional_info_desc")} className="form-control" />
            </div>
            {watch(`answer-${question.id}`)?.length > 2000 ? <InputWrapper.Error message={t("answer_max_2000_chars")} /> : null}
          </InputWrapper>
        </div>
      ) : null}
    </div>
  );

  const renderAllQuestions = (type: string) => {
    switch (type) {
      case QUESTION_TYPES.CAREER_BASED:
        return careerBasedQuestions?.questions?.map((question, index) => renderQuestion(question, index)) || [];
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return roleSpecificQuestions?.[selectedSkill.skillName]?.questions?.map((question, index) => renderQuestion(question, index)) || [];
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return cultureSpecificQuestions?.[selectedSkill.skillName]?.questions?.map((question, index) => renderQuestion(question, index)) || [];
      default:
        return [];
    }
  };

  // get skills based on type
  const getSkills = (type: string) => (type === QUESTION_TYPES.CULTURE_SPECIFIC ? cultureSkills : roleSkills);

  // function to switch main skill on click
  const switchMainSkill = (selectedSkill: string) => {
    if (selectedTab === QUESTION_TYPES.CAREER_BASED && !isSkillCompleted(QUESTION_TYPES.CAREER_BASED, "")) {
      return;
    }
    if (areAllSkillsLocked(selectedSkill)) {
      toast.dismiss();
      setAreAllSkillsInterviewed(true);
    } else {
      setAreAllSkillsInterviewed(false);
    }
    if (selectedSkill !== selectedTab) {
      setFollowUpSkill({
        skillType: "",
        skillTitle: "",
        skillId: undefined,
        questions: [],
      });
    }
    setSelectedTab(selectedSkill);
    // find the first skill which is not locked for role or culture specific
    const skills = getSkills(selectedSkill);
    const skill = skills.find((skill) => !isSkillLocked(selectedSkill, skill));
    if (skill) {
      handleSkillSelection(skill, selectedSkill);
    }
  };

  const addFollowUpQuestion = async (data: IAddInterviewSkillQuestion) => {
    try {
      setFollowupLoading(true);
      const response = await addInterviewSkillQuestion(data);

      if (response?.data?.success) {
        const newQuestion = response?.data?.data;

        console.log("=====>>>", newQuestion);

        dispatch(addFollowupQuestion(newQuestion));
        setFollowUpQuestion("");

        toastMessageSuccess(t(response?.data?.message));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setFollowupLoading(false);
    }
  };

  // function to add follow up skill
  const addFollowUpSkill = async (selectedQuestions: string[]) => {
    toast.dismiss();
    console.log("selectedQuestions=====", selectedQuestions);
    if (!jobApplicationId || !interviewId) return;

    if (!selectedQuestions.length) {
      toastMessageError(t("please_select_atleast_one_question"));
      return;
    }

    if (followUpSkill.skillType === QUESTION_TYPES.CAREER_BASED) {
      toastMessageError(t("can_not_add_new_skill_in_career_based_skill"));
      return;
    }
    try {
      setFollowupLoading(true);

      socketRef?.current?.emit(
        "addFollowUpSkill",
        {
          jobApplicationId,
          interviewId,
          questions: selectedQuestions,
          skillType: selectedTab,
          skillTitle: followUpSkill.skillTitle,
          skillId: followUpSkill.skillId,
        },
        (response: IAddFollowUpSkillResponse) => {
          console.log("addFollowUpSkill====response", response);
          // Store follow-up question for the currently open question (first one in the array)
          if (response.success && response.data) {
            const data = response.data;
            dispatch(addFollowupSkill({ interviewId, skill: data.skillTitle, skillType: data.skillType, questions: data.questions }));
            setFollowUpSkill({
              skillType: "",
              skillTitle: "",
              skillId: undefined,
              questions: [],
            });
            toastMessageSuccess(translate(response?.message));
          } else {
            toastMessageError(translate(response?.message));
          }
          setFollowupLoading(false);
        }
      );
    } catch {
      toastMessageError(t("something_went_wrong"));
    }
  };

  const isAdditionalSkillExist = useMemo(() => {
    return selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC
      ? Object.values(cultureSpecificQuestions).some((skill) => skill.isAdditionalSkill)
      : Object.values(roleSpecificQuestions).some((skill) => skill.isAdditionalSkill);
  }, [cultureSpecificQuestions, roleSpecificQuestions, selectedTab]);

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="inner-section video_call_section mt-5">
          <div className="row">
            <div className="col-lg-3 col-md-12 col-sm-12">
              <div className={style.video_participants_grid}>
                {/* <div> */}
                {/* Local User Video */}
                <div className={style.video_participant_box}>
                  <div className={style.participant_name}>{interviewerName}</div>
                  {tokenData && joining ? (
                    <LocalUser
                      audioTrack={localMicrophoneTrack}
                      videoTrack={localCameraTrack}
                      cameraOn={isCameraOn}
                      micOn={isMicOn}
                      className={style.video_feed}
                      playAudio={false}
                      playVideo={true}
                    />
                  ) : (
                    <div className={style.empty_video}>
                      <p>{t("initializing_your_video")}</p>
                    </div>
                  )}
                </div>

                {/* Remote User Video */}
                <div className={style.video_participant_box}>
                  {remoteUsers.length > 0 ? (
                    <>
                      <div className={style.participant_name}>{candidateName}</div>
                      <RemoteUser user={remoteUsers[0]} playAudio={true} playVideo={true} className={style.video_feed}></RemoteUser>
                    </>
                  ) : (
                    <div className={style.empty_video}>
                      <p>{t("waiting_for_candidate_to_join")}</p>
                    </div>
                  )}
                </div>
                {/* </div> */}
                {/* Video Controls */}
                <div className="videos-btn-container">
                  <Button className="videos-btn secondary" onClick={toggleCamera}>
                    <VideoCallIcon className="videos-btn-icon" isVideoMute={!isCameraOn} />
                    {t("cam")} {isCameraOn}
                  </Button>
                  <Button className="videos-btn secondary" onClick={toggleMic}>
                    <MicIcon className="videos-btn-icon" isMicMute={!isMicOn} />
                    {t("mic")} {isMicOn}
                  </Button>
                </div>
              </div>
            </div>
            <div className="col-lg-9 col-md-12 col-sm-12">
              <div className="interview-content">
                <form onSubmit={handleSubmit((data) => handleSaveAndNext(data as IInterviewQuestionFormValues))}>
                  <div className="row">
                    <div className="col-md-12">
                      <ProgressTracker
                        isRecording={isRecording}
                        elapsedTime={elapsedTime}
                        currentSkill={selectedTab}
                        switchMainSkill={switchMainSkill}
                        onRecordingChange={() => {
                          if (isRecording) {
                            handleStopRecording();
                            stopTranscription();
                          } else {
                            handleStartRecording();
                            startTranscription();
                          }
                        }}
                      />
                      <div className="common-page-head-section">
                        <div className="main-heading">
                          <h2 className="d-flex align-items-center">
                            {selectedTab !== QUESTION_TYPES.CAREER_BASED ? (
                              <Button className="clear-btn p-0 m-0" type="button">
                                <BackArrowIcon onClick={() => handlePreviousSkillInterview()} />
                              </Button>
                            ) : null}
                            {selectedTab === QUESTION_TYPES.CAREER_BASED ? (
                              <>
                                {t("career_based_skills_and_general_interview")} <span>{t("questions")}</span>
                              </>
                            ) : (
                              <>
                                {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC ? t("role_specific_interview") : t("culture_specific_interview")}{" "}
                                <span className="ps-1">{t("questions")}</span>
                              </>
                            )}
                          </h2>
                          <Button className="clear-btn text-btn primary p-0 m-0" onClick={() => window.open(resumeLink, "_blank")}>
                            <PreviewResumeIcon className="me-2" />
                            {t("preview_candidate_resume")}
                          </Button>
                        </div>
                        {!areAllSkillsInterviewed &&
                        (selectedTab === QUESTION_TYPES.ROLE_SPECIFIC || selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC) ? (
                          <>
                            <div className={style.question_info_box}>
                              <ul>
                                <li>
                                  <span className={style.current} />
                                  {t("current")}
                                </li>
                                <li>
                                  <span className={style.completed} />
                                  {t("completed")}
                                </li>
                                {isAdditionalSkillExist ? (
                                  <li>
                                    <span className={style.additional} />
                                    {t("additional")}
                                  </li>
                                ) : null}
                              </ul>
                            </div>
                            <div className="interview-topic-list">
                              <Swiper
                                navigation={{
                                  prevEl: ".custom-prev",
                                  nextEl: ".custom-next",
                                  enabled: true,
                                }}
                                pagination={{
                                  type: "fraction",
                                  el: ".swiper-pagination-text",
                                }}
                                slidesPerView={5.5}
                                spaceBetween={15}
                                className="mySwiper"
                                modules={[Pagination, Navigation]}
                              >
                                {getSkills(selectedTab)?.map((skill, index) => (
                                  <SwiperSlide
                                    key={skill}
                                    className={`topic-item ${skill === selectedSkill.skillName ? "current" : isSkillCompleted(selectedTab, skill) ? "completed" : isAdditionalSkill(skill) ? "additional" : ""}`}
                                    tabIndex={index}
                                    onClick={() => {
                                      if (!isSkillLocked(selectedTab, skill)) {
                                        handleSkillSelection(skill, selectedTab);
                                      }
                                    }}
                                  >
                                    {skill}
                                    {isSkillCompleted(selectedTab, skill) ? (
                                      <span className="interviewer-name">
                                        {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC
                                          ? roleSpecificQuestions[skill]?.interviewerName
                                          : cultureSpecificQuestions[skill]?.interviewerName}
                                      </span>
                                    ) : null}
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            </div>
                          </>
                        ) : null}
                      </div>
                    </div>

                    <div className={`col-lg-${areAllSkillsInterviewed ? 12 : 8} col-md-12 col-sm-12`}>
                      <div className="interview-question-cards-height">
                        {selectedTab !== QUESTION_TYPES.CAREER_BASED && areAllSkillsInterviewed ? null : renderAllQuestions(selectedTab)}
                        <div>
                          {selectedTab !== QUESTION_TYPES.CAREER_BASED && areAllSkillsInterviewed ? (
                            <div className="text-center py-4">
                              <Image src={allSkillInterviewed} alt="All skills interviewed" className="img-fluid" style={{ width: "50%" }} />
                            </div>
                          ) : (
                            <>
                              <div className="section-heading">
                                <h2>
                                  {t("score")}{" "}
                                  <span className="primary">
                                    {selectedTab === QUESTION_TYPES.CAREER_BASED ? t("career") : selectedSkill.skillName} {t("stratum")}
                                  </span>
                                </h2>
                                <p>
                                  {t("score_the_candidate_for")}{" "}
                                  {selectedTab === QUESTION_TYPES.CAREER_BASED ? t("career_based") : selectedSkill.skillName.toLowerCase()}{" "}
                                  {t("stratum")} {t("score_range")}
                                </p>
                              </div>
                              <ul className="number-task">
                                {Array.from({ length: 10 }).map((_, index) => (
                                  <li
                                    className={
                                      index > 8
                                        ? index + 1 === skillMarked
                                          ? "extreme active"
                                          : "extreme"
                                        : index + 1 === skillMarked
                                          ? "active"
                                          : ""
                                    }
                                    key={index}
                                    onClick={() => {
                                      if (loading) return;
                                      setSkillMarked(index + 1 === skillMarked ? 0 : index + 1);
                                      setIsError(false);
                                    }}
                                  >
                                    {index > 8 ? t("extreme") : index + 1}
                                  </li>
                                ))}
                              </ul>
                              {interviewStaticInformation && skillMarked ? (
                                <div className="interview-question-card">
                                  <h5>{skillMarked ? interviewStaticInformation?.stratumDescription[skillMarked] : ""}</h5>
                                </div>
                              ) : null}
                              {skillMarked ? null : <p className="common-error fs-5 mb-3">{isError ? t("please_mark_stratum_score") : ""}</p>}
                            </>
                          )}

                          <div className="button-align justify-content-between not-show-tab-mobile">
                            <div className="button-align ">
                              {!isLastSkillOverall() && isSkillCompleted(QUESTION_TYPES.CAREER_BASED, "") ? (
                                <Button className="primary-btn btn-xs" type="button" disabled={loading || loader} onClick={switchCategory}>
                                  {t("switch_category")}
                                </Button>
                              ) : null}
                              {!isSkillLocked(selectedTab, selectedSkill.skillName) && !areAllSkillsInterviewed ? (
                                <Button className="dark-outline-btn btn-xs" type="submit" disabled={loading || loader} loading={loading}>
                                  {isLastSkillOverall() && cultureSkills.indexOf(selectedSkill.skillName) === cultureSkills.length - 1
                                    ? t("save")
                                    : t("save_next")}
                                </Button>
                              ) : null}
                            </div>
                            {selectedTab !== QUESTION_TYPES.CAREER_BASED ? (
                              <Button
                                className="danger-btn btn-xs"
                                type="button"
                                onClick={() => setShowEndInterviewModal(true)}
                                disabled={loading || loader}
                              >
                                {t("end_interview")}
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                    </div>
                    {selectedTab !== QUESTION_TYPES.CAREER_BASED && areAllSkillsInterviewed ? null : (
                      <div className="col-lg-4 col-md-12 col-sm-12">
                        <div className="behavioral-letter-card">
                          <h5>
                            {t("behavioral")} <span>{t("performance")}</span>
                          </h5>
                          <InputWrapper>
                            {/* <InputWrapper.Label htmlFor="behavioralInfo">{t("describe_candidate_behaviours")}</InputWrapper.Label> */}
                            <Textarea
                              rows={10}
                              name="behavioralInfo"
                              control={control}
                              placeholder={t("describe_candidate_behaviours")}
                              className="form-control"
                            />
                            <InputWrapper.Error message={errors.behavioralInfo?.message as string} />
                          </InputWrapper>
                          <LetterFoldIcon className="fold-svg" />
                        </div>
                      </div>
                    )}

                    {/* This code is for responsiveness */}
                    <div className="col-sm-12 mt-5 not-show-after-tab">
                      <div className="button-align ">
                        {!isLastSkillOverall() && isSkillCompleted(QUESTION_TYPES.CAREER_BASED, "") ? (
                          <Button className="primary-btn btn-xs w-100" type="button" disabled={loading || loader} onClick={switchCategory}>
                            {t("switch_category")}
                          </Button>
                        ) : null}
                        {!isSkillLocked(selectedTab, selectedSkill.skillName) && !areAllSkillsInterviewed ? (
                          <Button className="dark-outline-btn btn-xs w-100" type="submit" disabled={loading || loader} loading={loading}>
                            {isLastSkillOverall() && cultureSkills.indexOf(selectedSkill.skillName) === cultureSkills.length - 1
                              ? t("save")
                              : t("save_next")}
                          </Button>
                        ) : null}
                      </div>
                      {selectedTab !== QUESTION_TYPES.CAREER_BASED ? (
                        <Button
                          className="danger-btn btn-xs w-100 mt-3"
                          type="button"
                          onClick={() => setShowEndInterviewModal(true)}
                          disabled={loading || loader}
                        >
                          {t("end_interview")}
                        </Button>
                      ) : null}
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      {showEndInterviewModal ? (
        <EndInterViewModal onClickCancel={() => setShowEndInterviewModal(false)} onClickEndInterview={handleEndInterview} disabled={loader} />
      ) : null}
      {followUpQuestion || followUpSkill?.questions?.length > 0 ? (
        <FollowUpModal
          followupQuestion={followUpQuestion}
          skillQuestions={followUpSkill?.questions}
          skillName={followUpSkill?.skillTitle}
          type={followUpSkill?.questions?.length > 0 ? FOLLOW_UP_TYPE.SKILL : FOLLOW_UP_TYPE.QUESTION}
          onProceed={(selectedQuestions) => {
            if (followUpSkill?.questions?.length > 0 && selectedQuestions) {
              addFollowUpSkill(selectedQuestions);
            } else {
              addFollowUpQuestion({
                jobApplicationId,
                question: followUpQuestion,
                skillType: elapsedTimeRef.current.type,
                interviewId,
                jobSkillId: elapsedTimeRef.current.jobSkillId,
                questionType: INTERVIEW_QUESTION_TYPE.FOLLOW_UP,
              });
            }
          }}
          loading={followupLoading}
          onCancel={() => {
            setFollowUpQuestion("");
            setFollowUpSkill({
              skillType: "",
              skillTitle: "",
              questions: [],
              skillId: undefined,
            });
          }}
        />
      ) : null}
    </div>
  );
};

export default Interview;
