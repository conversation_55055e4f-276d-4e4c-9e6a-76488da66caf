"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Image from "next/image";
import InterviewInfoImg from "../../../public/assets/images/interview-info.png";
import { INTERVIEW_SCHEDULE_ROUND_TYPE } from "@/constants/commonConstants";
import { IInterviewStaticInformation } from "@/interfaces/interviewInterfaces";
import { useTranslations } from "next-intl";
interface IProps {
  onClickCancel: () => void;
  onClickContinue: () => void;
  interviewStaticInformation: IInterviewStaticInformation;
  type: string;
  disabled?: boolean;
}

const ConductingInterviewsModal: FC<IProps> = ({ onClickCancel, onClickContinue, interviewStaticInformation, type }) => {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-header text-start pb-0">
            <h4>
              {t("best_practice_for")} <span>{t("conducting_interviews")}</span>
            </h4>
            <p className="m-0 textMd">{t("ensure_smooth_interview")}</p>
            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body position-relative  ">
            {/* interview-info */}
            <Image src={InterviewInfoImg} alt="InterviewInfoImg" className="interview-info-img" width={500} height={500} />
            <div className="interview-info">
              {type === INTERVIEW_SCHEDULE_ROUND_TYPE[0].value
                ? interviewStaticInformation.oneToOneInterviewInstructions.map((instruction, index) => (
                    <div className="info-item w-75" key={index}>
                      <h4 className="info-title">
                        <span className="dot" />
                        {instruction.title}
                      </h4>
                      <p className="fs-4">{instruction.content}</p>
                    </div>
                  ))
                : interviewStaticInformation.videoCallInterviewInstructions.map((instruction, index) => (
                    <div className="info-item w-75" key={index}>
                      <h4 className="info-title">
                        <span className="dot" />
                        {instruction.title}
                      </h4>
                      <p className="fs-4">{instruction.content}</p>
                    </div>
                  ))}
            </div>
            <div className="action-btn">
              <Button
                onClick={() => {
                  setLoading(true);
                  onClickContinue();
                }}
                className="primary-btn rounded-md"
                loading={loading}
                disabled={loading}
              >
                {t("continue")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ConductingInterviewsModal;
