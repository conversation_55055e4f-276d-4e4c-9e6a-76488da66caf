import { signOut } from "next-auth/react";
import Cookies from "js-cookie";
import CryptoJS from "crypto-js";
import toast from "react-hot-toast";

import storage from "./storage";

import { ACCESS_TOKEN_KEY, PERMISSIONS_COOKIES_KEY } from "@/constants/commonConstants";
import { deleteSession } from "@/services/authServices";
import { getSignedUrl } from "@/services/commonService";
import { FilePath } from "@/interfaces/commonInterfaces";

const encryptionKey = process.env.NEXT_PUBLIC_ENCRYPTION_KEY;

export const getAccessToken = () => {
  return storage.get(ACCESS_TOKEN_KEY);
};

export const clearStorage = () => {
  return storage.removeAll();
};

export const setAccessToken = (accessToken: string) => {
  storage.set(ACCESS_TOKEN_KEY, accessToken);
};

/**
 * Toast style object
 */
const style = {
  fontSize: "16px",
};

/**
 * Toast success message
 * @param message - The message to display
 */
export const toastMessageSuccess = (message: string) => {
  dismissAllToasts();
  toast.success(message, {
    style,
  });
};

/**
 * Toast success message with icon
 * @param message - The message to display
 * @param icon - The icon to display
 */
export const toastMessageWithIcon = (message: string, icon: string) => {
  dismissAllToasts();
  toast.success(message, {
    style,
    icon,
  });
};

/**
 * Toast error message
 * @param message - The message to display
 */
export const toastMessageError = (message: string) => {
  dismissAllToasts();
  toast.error(message, {
    style,
  });
};

/**
 * Dismiss all existing toast notifications
 */
export const dismissAllToasts = () => {
  toast.dismiss();
};

export const logout = async (userId?: number) => {
  try {
    deleteSession(userId);
    await signOut({ redirect: false });
    clearStorage();

    // Delete permissions_data cookies when user logs out
    Cookies.remove(PERMISSIONS_COOKIES_KEY, { path: "/" });
  } catch (error) {
    console.error("Error in logout:", error);
  }
};

/**
 *  get presignedUrl for image upload
 */
export const uploadFileOnS3 = async (file: Blob, filePath: string) => {
  let body: FilePath = {
    filePath: "",
    fileFormat: "",
  };
  body = {
    filePath,
    fileFormat: file.type as string,
  };
  let signedUrl;
  const presignedUrl = await getSignedUrl(body);
  if (presignedUrl && presignedUrl.data) {
    const response = await pushFileToS3(presignedUrl.data.data, file);
    if (response?.url) {
      signedUrl = response?.url.split("?")?.[0];
    }
  }

  return signedUrl?.replace(`${process.env.NEXT_PUBLIC_S3_URL}`, `${process.env.NEXT_PUBLIC_S3_CDN_URL}`);
};

/**
 *  Upload file on presignedUrl of S3
 */
export const pushFileToS3 = async (signedUrl: string, file: Blob): Promise<Response> => {
  return fetch(signedUrl, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Format times as HH:MM for time inputs
export const formatTimeForInput = (date: Date) => {
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
};

// Format the elapsed time as HH:MM:SS
export const formatTime = (timeInSeconds: number): string => {
  const hours = Math.floor(timeInSeconds / 3600);
  const minutes = Math.floor((timeInSeconds % 3600) / 60);
  const seconds = timeInSeconds % 60;

  return [hours, minutes, seconds].map((val) => val.toString().padStart(2, "0")).join(":");
};

export const encryptInfo = (info: string) => {
  if (encryptionKey) {
    console.log("inside encryptInfo");
    return CryptoJS.AES.encrypt(info, encryptionKey).toString();
  }
  return "";
};

export const decryptInfo = (info: string) => {
  if (encryptionKey) {
    console.log("inside decryptInfo");
    const bytes = CryptoJS.AES.decrypt(decodeURIComponent(info), encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }
  return "";
};

export const toTitleCase = (name: string) => {
  if (!name) return "";
  return name
    .toLowerCase()
    .split(" ")
    .filter((word) => word) // remove extra spaces
    .map((word) => word[0].toUpperCase() + word.slice(1))
    .join(" ");
};

// Normalize spaces (replace multiple spaces with a single space)
export const normalizeSpaces = (text: string): string => {
  return text.trim().replace(/\s+/g, " ");
};

export const getOrdinalSuffix = (number: number): string => {
  const j = number % 10;
  const k = number % 100;
  if (j === 1 && k !== 11) {
    return "st";
  }
  if (j === 2 && k !== 12) {
    return "nd";
  }
  if (j === 3 && k !== 13) {
    return "rd";
  }
  return "th";
};

/**
 * Generate user initials from first and last name
 * @param firstName - User's first name
 * @param lastName - User's last name
 * @returns String containing initials (e.g., "JD" for John Doe, "J" for John, "U" as fallback)
 */
export const getUserInitials = (firstName?: string, lastName?: string): string => {
  if (!firstName && !lastName) return "U";

  const firstInitial = firstName?.charAt(0).toUpperCase() || "";
  const lastInitial = lastName?.charAt(0).toUpperCase() || "";

  return firstInitial + lastInitial || firstInitial || "U";
};
