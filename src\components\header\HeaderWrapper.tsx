"use client";
import { usePathname } from "next/navigation";
import Header from "@/components/header/Header";
import { BEFORE_LOGIN_ROUTES } from "@/constants/routes";
import HomeHeader from "./HomeHeader";

export default function HeaderWrapper() {
  const pathname = usePathname();

  console.log("Header wrapper pathname", pathname);
  // show before login header if user is on before login page
  if (BEFORE_LOGIN_ROUTES.includes(pathname!)) {
    return <HomeHeader />;
  }

  // show after login header if user is not on before login page
  return <Header />;
}
